2025-06-06T12:33:06.336+0200 [DEBUG] [org.gradle.launcher.daemon.bootstrap.DaemonMain] Assuming the daemon was started with following jvm opts: [-Xmx2048m, -Dfile.encoding=UTF-8, -Duser.country=US, -Duser.language=en, -Duser.variant]
2025-06-06T12:33:06.481+0200 [INFO] [org.gradle.launcher.daemon.server.Daemon] start() called on daemon - DefaultDaemonContext[uid=7d9a3a7e-8d69-4175-a659-895ebd476cf2,javaHome=/Users/<USER>/Library/Java/JavaVirtualMachines/ms-17.0.15/Contents/Home,javaVersion=17,javaVendor=Microsoft,daemonRegistryDir=/Volumes/wadie/Smart Shield/android-app/gradle/daemon,pid=1542,idleTimeout=10800000,priority=NORMAL,applyInstrumentationAgent=true,nativeServicesMode=ENABLED,daemonOpts=-Xmx2048m,-Dfile.encoding=UTF-8,-Duser.country=US,-Duser.language=en,-Duser.variant]
2025-06-06T12:33:06.539+0200 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface utun3
2025-06-06T12:33:06.539+0200 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2025-06-06T12:33:06.540+0200 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:ce81:b1c:bd2c:69e%utun3
2025-06-06T12:33:06.541+0200 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface utun2
2025-06-06T12:33:06.541+0200 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2025-06-06T12:33:06.542+0200 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:afe:3a45:bc2e:a182%utun2
2025-06-06T12:33:06.543+0200 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface utun1
2025-06-06T12:33:06.543+0200 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2025-06-06T12:33:06.543+0200 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:8ca5:d39d:a089:d25a%utun1
2025-06-06T12:33:06.544+0200 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface utun0
2025-06-06T12:33:06.544+0200 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2025-06-06T12:33:06.545+0200 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:3a1:bebc:459a:b283%utun0
2025-06-06T12:33:06.545+0200 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface en1
2025-06-06T12:33:06.545+0200 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2025-06-06T12:33:06.545+0200 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:1072:6c73:a6d8:fce3%en1
2025-06-06T12:33:06.546+0200 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /************
2025-06-06T12:33:06.546+0200 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface lo0
2025-06-06T12:33:06.546+0200 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? true
2025-06-06T12:33:06.546+0200 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Ignoring remote address on loopback interface /fe80:0:0:0:0:0:0:1%lo0
2025-06-06T12:33:06.547+0200 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding loopback address /0:0:0:0:0:0:0:1%lo0
2025-06-06T12:33:06.547+0200 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding loopback address /127.0.0.1
2025-06-06T12:33:06.555+0200 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Listening on [7fd0d67e-5e88-4741-900e-f8b71f9c6ac0 port:50623, addresses:[localhost/127.0.0.1]].
2025-06-06T12:33:06.582+0200 [DEBUG] [org.gradle.launcher.daemon.server.Daemon] Daemon starting at: Fri Jun 06 12:33:06 CEST 2025, with address: [7fd0d67e-5e88-4741-900e-f8b71f9c6ac0 port:50623, addresses:[localhost/127.0.0.1]]
2025-06-06T12:33:06.582+0200 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Advertising the daemon address to the clients: [7fd0d67e-5e88-4741-900e-f8b71f9c6ac0 port:50623, addresses:[localhost/127.0.0.1]]
2025-06-06T12:33:06.582+0200 [DEBUG] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Advertised daemon context: DefaultDaemonContext[uid=7d9a3a7e-8d69-4175-a659-895ebd476cf2,javaHome=/Users/<USER>/Library/Java/JavaVirtualMachines/ms-17.0.15/Contents/Home,javaVersion=17,javaVendor=Microsoft,daemonRegistryDir=/Volumes/wadie/Smart Shield/android-app/gradle/daemon,pid=1542,idleTimeout=10800000,priority=NORMAL,applyInstrumentationAgent=true,nativeServicesMode=ENABLED,daemonOpts=-Xmx2048m,-Dfile.encoding=UTF-8,-Duser.country=US,-Duser.language=en,-Duser.variant]
2025-06-06T12:33:06.584+0200 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Storing daemon address: [7fd0d67e-5e88-4741-900e-f8b71f9c6ac0 port:50623, addresses:[localhost/127.0.0.1]], context: DefaultDaemonContext[uid=7d9a3a7e-8d69-4175-a659-895ebd476cf2,javaHome=/Users/<USER>/Library/Java/JavaVirtualMachines/ms-17.0.15/Contents/Home,javaVersion=17,javaVendor=Microsoft,daemonRegistryDir=/Volumes/wadie/Smart Shield/android-app/gradle/daemon,pid=1542,idleTimeout=10800000,priority=NORMAL,applyInstrumentationAgent=true,nativeServicesMode=ENABLED,daemonOpts=-Xmx2048m,-Dfile.encoding=UTF-8,-Duser.country=US,-Duser.language=en,-Duser.variant]
2025-06-06T12:33:06.609+0200 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2025-06-06T12:33:06.613+0200 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2025-06-06T12:33:06.617+0200 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2025-06-06T12:33:06.618+0200 [LIFECYCLE] [org.gradle.launcher.daemon.server.Daemon] Daemon server started.
2025-06-06T12:33:06.621+0200 [DEBUG] [org.gradle.launcher.daemon.bootstrap.DaemonStartupCommunication] Completed writing the daemon greeting. Closing streams...
2025-06-06T12:33:06.633+0200 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Accepted connection from /127.0.0.1:50624 to /127.0.0.1:50623.
2025-06-06T12:33:06.645+0200 [DEBUG] [org.gradle.launcher.daemon.server.Daemon] stopOnExpiration() called on daemon
2025-06-06T12:33:06.648+0200 [DEBUG] [org.gradle.launcher.daemon.server.Daemon] awaitExpiration() called on daemon
2025-06-06T12:33:06.649+0200 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2025-06-06T12:33:06.692+0200 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 21: received class org.gradle.launcher.daemon.protocol.Build
2025-06-06T12:33:06.693+0200 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 21: Received non-IO message from client: Build{id=424aacc1-a517-4f36-81d9-5a342f54e3d6, currentDir=/Volumes/wadie/Smart Shield/android-app}
2025-06-06T12:33:06.693+0200 [INFO] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received command: Build{id=424aacc1-a517-4f36-81d9-5a342f54e3d6, currentDir=/Volumes/wadie/Smart Shield/android-app}.
2025-06-06T12:33:06.694+0200 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Starting executing command: Build{id=424aacc1-a517-4f36-81d9-5a342f54e3d6, currentDir=/Volumes/wadie/Smart Shield/android-app} with connection: socket connection from /127.0.0.1:50623 to /127.0.0.1:50624.
2025-06-06T12:33:06.698+0200 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: started DaemonCommandExecution[command = Build{id=424aacc1-a517-4f36-81d9-5a342f54e3d6, currentDir=/Volumes/wadie/Smart Shield/android-app}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:50623 to /127.0.0.1:50624] after 0.0031666666666666666 minutes of idle
2025-06-06T12:33:06.698+0200 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Marking the daemon as busy, address: [7fd0d67e-5e88-4741-900e-f8b71f9c6ac0 port:50623, addresses:[localhost/127.0.0.1]]
2025-06-06T12:33:06.698+0200 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Marking busy by address: [7fd0d67e-5e88-4741-900e-f8b71f9c6ac0 port:50623, addresses:[localhost/127.0.0.1]]
2025-06-06T12:33:06.699+0200 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2025-06-06T12:33:06.699+0200 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2025-06-06T12:33:06.700+0200 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2025-06-06T12:33:06.700+0200 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] resetting idle timer
2025-06-06T12:33:06.700+0200 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2025-06-06T12:33:06.702+0200 [INFO] [org.gradle.launcher.daemon.server.exec.StartBuildOrRespondWithBusy] Daemon is about to start building Build{id=424aacc1-a517-4f36-81d9-5a342f54e3d6, currentDir=/Volumes/wadie/Smart Shield/android-app}. Dispatching build started information...
2025-06-06T12:33:06.702+0200 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 23: dispatching org.gradle.launcher.daemon.protocol.BuildStarted@e04b6d2
2025-06-06T12:33:06.706+0200 [DEBUG] [org.gradle.launcher.daemon.server.exec.EstablishBuildEnvironment] Configuring env variables: [PATH, __CFBundleIdentifier, SHELL, OLDPWD, SECURITYSESSIONID, USER, COMMAND_MODE, LaunchInstanceID, TMPDIR, SSH_AUTH_SOCK, XPC_FLAGS, __CF_USER_TEXT_ENCODING, LOGNAME, LC_CTYPE, XPC_SERVICE_NAME, HOME]
2025-06-06T12:33:06.710+0200 [DEBUG] [org.gradle.launcher.daemon.server.exec.LogToClient] About to start relaying all logs to the client via the connection.
2025-06-06T12:33:06.711+0200 [INFO] [org.gradle.launcher.daemon.server.exec.LogToClient] The client will now receive all logging from the daemon (pid: 1542). The daemon log file: /Volumes/wadie/Smart Shield/android-app/gradle/daemon/8.11.1/daemon-1542.out.log
2025-06-06T12:33:06.714+0200 [INFO] [org.gradle.launcher.daemon.server.exec.LogAndCheckHealth] Starting build in new daemon [memory: 2 GiB]
2025-06-06T12:33:06.717+0200 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] The daemon has started executing the build.
2025-06-06T12:33:06.718+0200 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] Executing build with daemon context: DefaultDaemonContext[uid=7d9a3a7e-8d69-4175-a659-895ebd476cf2,javaHome=/Users/<USER>/Library/Java/JavaVirtualMachines/ms-17.0.15/Contents/Home,javaVersion=17,javaVendor=Microsoft,daemonRegistryDir=/Volumes/wadie/Smart Shield/android-app/gradle/daemon,pid=1542,idleTimeout=10800000,priority=NORMAL,applyInstrumentationAgent=true,nativeServicesMode=ENABLED,daemonOpts=-Xmx2048m,-Dfile.encoding=UTF-8,-Duser.country=US,-Duser.language=en,-Duser.variant]
