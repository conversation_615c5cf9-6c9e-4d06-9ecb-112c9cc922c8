BUILD_FAILED=true
BUILD_FINISH_TIME=1749206683537
BUILD_SRC_EXISTS=false
COMPILATION_STARTED=true
CONFIGURATION_API_COUNT=1
CONFIGURATION_IMPLEMENTATION_COUNT=1
CPU_NUMBER_OF_CORES=8
ENABLED_COMPILER_PLUGIN_PARSELIZE=true
EXECUTED_FROM_IDEA=true
GRADLE_BUILD_DURATION=30548
GRADLE_BUILD_NUMBER_IN_CURRENT_DAEMON=3
GRADLE_DAEMON_HEAP_SIZE=2000000000
GRADLE_EXECUTION_DURATION=30232
GRADLE_NUMBER_OF_TASKS=200
GRADLE_NUMBER_OF_UNCONFIGURED_TASKS=200
GRADLE_VERSION=8.11.1
JVM_DEFAULTS=disable
KOTLIN_COMPILER_VERSION=1.9.22
KOTLIN_KTS_USED=false
KOTLIN_PROGRESSIVE_MODE=false
KOTLIN_STDLIB_VERSION=1.9.22
NUMBER_OF_SUBPROJECTS=1
OS_TYPE=Mac OS X
PROJECT_PATH=/Volumes/wadie/Smart Shield/android-app
STATISTICS_COLLECT_METRICS_OVERHEAD=4
STATISTICS_VISIT_ALL_PROJECTS_OVERHEAD=3
USE_CLASSPATH_SNAPSHOT=default-true
USE_FIR=false
BUILD FINISHED
BUILD_FAILED=true
BUILD_FINISH_TIME=1749208386635
BUILD_SRC_EXISTS=false
COMPILATION_STARTED=true
CONFIGURATION_API_COUNT=1
CONFIGURATION_IMPLEMENTATION_COUNT=1
CPU_NUMBER_OF_CORES=8
ENABLED_COMPILER_PLUGIN_PARSELIZE=true
EXECUTED_FROM_IDEA=true
GRADLE_BUILD_DURATION=45182
GRADLE_BUILD_NUMBER_IN_CURRENT_DAEMON=4
GRADLE_DAEMON_HEAP_SIZE=2000000000
GRADLE_EXECUTION_DURATION=44517
GRADLE_NUMBER_OF_TASKS=200
GRADLE_NUMBER_OF_UNCONFIGURED_TASKS=200
GRADLE_VERSION=8.11.1
JVM_DEFAULTS=disable
KOTLIN_COMPILER_VERSION=1.9.22
KOTLIN_KTS_USED=false
KOTLIN_PROGRESSIVE_MODE=false
KOTLIN_STDLIB_VERSION=1.9.22
NUMBER_OF_SUBPROJECTS=1
OS_TYPE=Mac OS X
PROJECT_PATH=/Volumes/wadie/Smart Shield/android-app
STATISTICS_COLLECT_METRICS_OVERHEAD=2
STATISTICS_VISIT_ALL_PROJECTS_OVERHEAD=3
USE_CLASSPATH_SNAPSHOT=default-true
USE_FIR=false
BUILD FINISHED
