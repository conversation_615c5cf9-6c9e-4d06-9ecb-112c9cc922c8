Gradle is a build tool with a focus on build automation and support for multi-language development. If you are building, testing, publishing, and deploying software on any platform, Gradle offers a flexible model that can support the entire development lifecycle from compiling and packaging code to publishing web sites. Gradle has been designed to support build automation across multiple languages and platforms including Java, Scala, Android, C/C++, and Groovy, and is closely integrated with development tools and continuous integration servers including Eclipse, IntelliJ, and Jenkins.

For more information about <PERSON>rad<PERSON>, please visit: https://gradle.org

If you are using the "all" distribution, the User Manual is included in your distribution.

If you are using the "bin" distribution, a copy of the User Manual is available on https://docs.gradle.org.

Typing `gradle help` prints the command line help.

Typing `gradle tasks` shows all the tasks of a Gradle build.
