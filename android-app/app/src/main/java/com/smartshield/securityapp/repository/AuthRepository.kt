package com.smartshield.securityapp.repository

import android.content.Context
import com.smartshield.securityapp.network.ApiService
import com.smartshield.securityapp.network.NetworkResult
import com.smartshield.securityapp.network.models.*
import com.smartshield.securityapp.network.safeApiCall
import com.smartshield.securityapp.utils.TokenManager
import com.smartshield.securityapp.utils.getDeviceInfo
import com.smartshield.securityapp.utils.DeviceInfo
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import timber.log.Timber

class AuthRepository(
    private val apiService: ApiService,
    private val tokenManager: TokenManager,
    private val context: Context
) {
    
    suspend fun register(
        email: String,
        password: String,
        fullName: String,
        phone: String? = null
    ): Flow<NetworkResult<AuthResponse>> = flow {
        emit(NetworkResult.Loading())
        
        try {
            val deviceInfo = context.getDeviceInfo()
            val request = RegisterRequest(
                email = email,
                password = password,
                fullName = fullName,
                phone = phone
            )
            
            val result = safeApiCall { apiService.register(request) }
            
            when (result) {
                is NetworkResult.Success -> {
                    val authResponse = result.data
                    if (authResponse.success && authResponse.accessToken != null) {
                        // Save tokens
                        tokenManager.saveTokens(
                            authResponse.accessToken,
                            authResponse.refreshToken ?: "",
                            authResponse.expiresIn
                        )
                        
                        // Save user info
                        authResponse.user?.let { user ->
                            tokenManager.saveUserInfo(user.id, user.email, user.fullName)
                        }
                        
                        // Register device after successful registration
                        registerDeviceAfterAuth(authResponse.accessToken, deviceInfo)
                        
                        Timber.d("Registration successful for: $email")
                    }
                    emit(result)
                }
                is NetworkResult.Error -> {
                    Timber.e("Registration failed: ${result.message}")
                    emit(result)
                }
                is NetworkResult.Loading -> emit(result)
            }
        } catch (e: Exception) {
            Timber.e(e, "Registration error")
            emit(NetworkResult.Error("Registration failed: ${e.message}"))
        }
    }
    
    suspend fun login(
        email: String,
        password: String
    ): Flow<NetworkResult<AuthResponse>> = flow {
        emit(NetworkResult.Loading())
        
        try {
            val deviceInfo = context.getDeviceInfo()
            val request = LoginRequest(
                email = email,
                password = password,
                deviceInfo = DeviceInfo(
                    deviceModel = deviceInfo.deviceModel,
                    deviceBrand = deviceInfo.deviceBrand,
                    androidVersion = deviceInfo.androidVersion,
                    appVersion = deviceInfo.appVersion,
                    deviceId = deviceInfo.deviceId
                )
            )
            
            val result = safeApiCall { apiService.login(request) }
            
            when (result) {
                is NetworkResult.Success -> {
                    val authResponse = result.data
                    if (authResponse.success && authResponse.accessToken != null) {
                        // Save tokens
                        tokenManager.saveTokens(
                            authResponse.accessToken,
                            authResponse.refreshToken ?: "",
                            authResponse.expiresIn
                        )
                        
                        // Save user info
                        authResponse.user?.let { user ->
                            tokenManager.saveUserInfo(user.id, user.email, user.fullName)
                        }
                        
                        // Register/update device after successful login
                        registerDeviceAfterAuth(authResponse.accessToken, deviceInfo)
                        
                        Timber.d("Login successful for: $email")
                    }
                    emit(result)
                }
                is NetworkResult.Error -> {
                    Timber.e("Login failed: ${result.message}")
                    emit(result)
                }
                is NetworkResult.Loading -> emit(result)
            }
        } catch (e: Exception) {
            Timber.e(e, "Login error")
            emit(NetworkResult.Error("Login failed: ${e.message}"))
        }
    }

    suspend fun googleAuth(
        idToken: String
    ): Flow<NetworkResult<AuthResponse>> = flow {
        emit(NetworkResult.Loading())

        try {
            val deviceInfo = context.getDeviceInfo()
            val request = GoogleAuthRequest(
                idToken = idToken,
                deviceInfo = DeviceInfo(
                    deviceModel = deviceInfo.deviceModel,
                    deviceBrand = deviceInfo.deviceBrand,
                    androidVersion = deviceInfo.androidVersion,
                    appVersion = deviceInfo.appVersion,
                    deviceId = deviceInfo.deviceId
                )
            )

            val result = safeApiCall { apiService.googleAuth(request) }

            when (result) {
                is NetworkResult.Success -> {
                    val authResponse = result.data
                    if (authResponse.success && authResponse.accessToken != null) {
                        // Save tokens
                        tokenManager.saveTokens(
                            authResponse.accessToken,
                            authResponse.refreshToken ?: "",
                            authResponse.expiresIn
                        )

                        // Save user info
                        authResponse.user?.let { user ->
                            tokenManager.saveUserInfo(user.id, user.email, user.fullName)
                        }

                        // Register/update device after successful login
                        registerDeviceAfterAuth(authResponse.accessToken, deviceInfo)

                        Timber.d("Google authentication successful")
                    }
                    emit(result)
                }
                is NetworkResult.Error -> {
                    Timber.e("Google authentication failed: ${result.message}")
                    emit(result)
                }
                is NetworkResult.Loading -> emit(result)
            }
        } catch (e: Exception) {
            Timber.e(e, "Google authentication error")
            emit(NetworkResult.Error("Google authentication failed: ${e.message}"))
        }
    }
    
    suspend fun refreshToken(): Flow<NetworkResult<AuthResponse>> = flow {
        emit(NetworkResult.Loading())
        
        try {
            val refreshToken = tokenManager.getRefreshToken()
            if (refreshToken == null) {
                emit(NetworkResult.Error("No refresh token available"))
                return@flow
            }
            
            val request = RefreshTokenRequest(refreshToken)
            val result = safeApiCall { apiService.refreshToken(request) }
            
            when (result) {
                is NetworkResult.Success -> {
                    val authResponse = result.data
                    if (authResponse.success && authResponse.accessToken != null) {
                        tokenManager.saveTokens(
                            authResponse.accessToken,
                            authResponse.refreshToken ?: refreshToken,
                            authResponse.expiresIn
                        )
                        Timber.d("Token refreshed successfully")
                    }
                    emit(result)
                }
                is NetworkResult.Error -> {
                    Timber.e("Token refresh failed: ${result.message}")
                    // Clear tokens if refresh fails
                    tokenManager.clearTokens()
                    emit(result)
                }
                is NetworkResult.Loading -> emit(result)
            }
        } catch (e: Exception) {
            Timber.e(e, "Token refresh error")
            tokenManager.clearTokens()
            emit(NetworkResult.Error("Token refresh failed: ${e.message}"))
        }
    }
    
    suspend fun getProfile(): Flow<NetworkResult<UserProfile>> = flow {
        emit(NetworkResult.Loading())
        
        try {
            val token = tokenManager.getAccessToken()
            if (token == null) {
                emit(NetworkResult.Error("No access token available"))
                return@flow
            }
            
            val result = safeApiCall { apiService.getProfile("Bearer $token") }
            emit(result)
        } catch (e: Exception) {
            Timber.e(e, "Get profile error")
            emit(NetworkResult.Error("Failed to get profile: ${e.message}"))
        }
    }
    
    suspend fun updateProfile(
        fullName: String? = null,
        phone: String? = null,
        emergencyContacts: List<EmergencyContact>? = null
    ): Flow<NetworkResult<UserProfile>> = flow {
        emit(NetworkResult.Loading())
        
        try {
            val token = tokenManager.getAccessToken()
            if (token == null) {
                emit(NetworkResult.Error("No access token available"))
                return@flow
            }
            
            val request = UpdateProfileRequest(
                fullName = fullName,
                phone = phone,
                emergencyContacts = emergencyContacts
            )
            
            val result = safeApiCall { apiService.updateProfile("Bearer $token", request) }
            
            when (result) {
                is NetworkResult.Success -> {
                    // Update local user info
                    result.data.let { user ->
                        tokenManager.saveUserInfo(user.id, user.email, user.fullName)
                    }
                    emit(result)
                }
                is NetworkResult.Error -> emit(result)
                is NetworkResult.Loading -> emit(result)
            }
        } catch (e: Exception) {
            Timber.e(e, "Update profile error")
            emit(NetworkResult.Error("Failed to update profile: ${e.message}"))
        }
    }
    
    suspend fun logout(): Flow<NetworkResult<ApiResponse>> = flow {
        emit(NetworkResult.Loading())
        
        try {
            val token = tokenManager.getAccessToken()
            if (token != null) {
                // Try to logout from server
                val result = safeApiCall { apiService.logout("Bearer $token") }
                emit(result)
            } else {
                emit(NetworkResult.Success(ApiResponse(success = true, message = "Logged out locally")))
            }
        } catch (e: Exception) {
            Timber.e(e, "Logout error")
            // Even if server logout fails, clear local data
            emit(NetworkResult.Success(ApiResponse(success = true, message = "Logged out locally")))
        } finally {
            // Always clear local data
            tokenManager.clearAllData()
            Timber.d("User logged out and data cleared")
        }
    }
    
    suspend fun changePassword(
        currentPassword: String,
        newPassword: String
    ): Flow<NetworkResult<ApiResponse>> = flow {
        emit(NetworkResult.Loading())
        
        try {
            val token = tokenManager.getAccessToken()
            if (token == null) {
                emit(NetworkResult.Error("No access token available"))
                return@flow
            }
            
            val request = ChangePasswordRequest(currentPassword, newPassword)
            val result = safeApiCall { apiService.changePassword("Bearer $token", request) }
            emit(result)
        } catch (e: Exception) {
            Timber.e(e, "Change password error")
            emit(NetworkResult.Error("Failed to change password: ${e.message}"))
        }
    }
    
    private suspend fun registerDeviceAfterAuth(accessToken: String, deviceInfo: DeviceInfo) {
        try {
            val deviceRequest = DeviceRegistrationRequest(
                deviceName = "${deviceInfo.deviceBrand} ${deviceInfo.deviceModel}",
                deviceModel = deviceInfo.deviceModel,
                deviceBrand = deviceInfo.deviceBrand,
                androidVersion = deviceInfo.androidVersion,
                appVersion = deviceInfo.appVersion,
                deviceId = deviceInfo.deviceId
            )
            
            val result = safeApiCall { apiService.registerDevice("Bearer $accessToken", deviceRequest) }
            
            when (result) {
                is NetworkResult.Success -> {
                    result.data.device?.let { device ->
                        tokenManager.saveDeviceInfo(device.apiKey, device.id)
                        Timber.d("Device registered successfully: ${device.id}")
                    }
                }
                is NetworkResult.Error -> {
                    Timber.e("Device registration failed: ${result.message}")
                }
                is NetworkResult.Loading -> {}
            }
        } catch (e: Exception) {
            Timber.e(e, "Device registration error")
        }
    }
    
    fun isLoggedIn(): Boolean {
        return tokenManager.isLoggedIn()
    }
    
    fun getCurrentUserId(): String? {
        return tokenManager.getUserId()
    }
    
    fun getCurrentUserEmail(): String? {
        return tokenManager.getUserEmail()
    }
    
    fun getCurrentUserName(): String? {
        return tokenManager.getUserName()
    }
}
