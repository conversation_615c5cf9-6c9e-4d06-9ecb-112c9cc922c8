package com.smartshield.securityapp.ui.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.blur
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.center
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.rotate
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import com.smartshield.securityapp.ui.theme.SmartShieldColors
import kotlin.math.*
import kotlin.random.Random
import androidx.compose.foundation.layout.RowScope

@Composable
fun CyberpunkBackground(
    modifier: Modifier = Modifier,
    showNetworkLines: Boolean = true,
    showParticles: Boolean = true,
    showGlowEffects: Boolean = true
) {
    val infiniteTransition = rememberInfiniteTransition(label = "cyberpunk_bg")
    
    // Animation values
    val rotation by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(20000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "rotation"
    )
    
    val pulseAlpha by infiniteTransition.animateFloat(
        initialValue = 0.3f,
        targetValue = 0.8f,
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "pulse"
    )
    
    val scanlineOffset by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(3000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "scanline"
    )

    Box(modifier = modifier) {
        // Base dark gradient background
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    brush = Brush.radialGradient(
                        colors = listOf(
                            SmartShieldColors.Background,
                            SmartShieldColors.BackgroundSecondary,
                            SmartShieldColors.Background
                        ),
                        radius = 800f
                    )
                )
        )
        
        // Animated cyberpunk elements
        Canvas(
            modifier = Modifier.fillMaxSize()
        ) {
            val centerX = size.width / 2
            val centerY = size.height / 2
            
            if (showGlowEffects) {
                drawGlowEffects(centerX, centerY, rotation, pulseAlpha)
            }
            
            if (showNetworkLines) {
                drawNetworkLines(centerX, centerY, rotation)
            }
            
            if (showParticles) {
                drawFloatingParticles(rotation, pulseAlpha)
            }
            
            // Scanning effect
            drawScanlines(scanlineOffset, pulseAlpha)
        }
        
        // Subtle overlay for depth
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    brush = Brush.verticalGradient(
                        colors = listOf(
                            Color.Transparent,
                            SmartShieldColors.Background.copy(alpha = 0.3f),
                            Color.Transparent
                        )
                    )
                )
        )
    }
}

private fun DrawScope.drawGlowEffects(
    centerX: Float,
    centerY: Float,
    rotation: Float,
    pulseAlpha: Float
) {
    // Central glow orb
    val glowRadius = 150f
    val glowPaint = Paint().apply {
        shader = RadialGradientShader(
            center = Offset(centerX, centerY),
            radius = glowRadius,
            colors = listOf(
                SmartShieldColors.Primary.copy(alpha = pulseAlpha * 0.8f),
                SmartShieldColors.PrimaryGlow.copy(alpha = pulseAlpha * 0.4f),
                Color.Transparent
            ),
            colorStops = listOf(0f, 0.7f, 1f)
        )
    }
    
    drawCircle(
        color = Color.Transparent,
        radius = glowRadius,
        center = Offset(centerX, centerY),
        style = androidx.compose.ui.graphics.drawscope.Fill
    )
    
    // Rotating energy rings
    repeat(3) { ring ->
        val ringRadius = 100f + (ring * 50f)
        val ringAlpha = pulseAlpha * (0.6f - ring * 0.1f)
        
        rotate(rotation + (ring * 120f), pivot = Offset(centerX, centerY)) {
            drawCircle(
                color = SmartShieldColors.Secondary.copy(alpha = ringAlpha),
                radius = ringRadius,
                center = Offset(centerX, centerY),
                style = androidx.compose.ui.graphics.drawscope.Stroke(width = 2f)
            )
        }
    }
}

private fun DrawScope.drawNetworkLines(
    centerX: Float,
    centerY: Float,
    rotation: Float
) {
    val lineCount = 12
    val maxRadius = maxOf(size.width, size.height) * 0.6f
    
    repeat(lineCount) { i ->
        val angle = (i * 360f / lineCount) + rotation * 0.5f
        val radians = Math.toRadians(angle.toDouble())
        
        val startRadius = 80f
        val endRadius = maxRadius
        
        val startX = centerX + cos(radians).toFloat() * startRadius
        val startY = centerY + sin(radians).toFloat() * startRadius
        val endX = centerX + cos(radians).toFloat() * endRadius
        val endY = centerY + sin(radians).toFloat() * endRadius
        
        // Create gradient for the line
        val lineGradient = Brush.linearGradient(
            colors = listOf(
                SmartShieldColors.NetworkLines.copy(alpha = 0.8f),
                SmartShieldColors.NetworkLines.copy(alpha = 0.3f),
                Color.Transparent
            ),
            start = Offset(startX, startY),
            end = Offset(endX, endY)
        )
        
        drawLine(
            brush = lineGradient,
            start = Offset(startX, startY),
            end = Offset(endX, endY),
            strokeWidth = 1.5f
        )
        
        // Add connection nodes
        if (i % 3 == 0) {
            val nodeX = centerX + cos(radians).toFloat() * (startRadius + 60f)
            val nodeY = centerY + sin(radians).toFloat() * (startRadius + 60f)
            
            drawCircle(
                color = SmartShieldColors.Primary.copy(alpha = 0.7f),
                radius = 3f,
                center = Offset(nodeX, nodeY)
            )
        }
    }
}

private fun DrawScope.drawFloatingParticles(
    rotation: Float,
    pulseAlpha: Float
) {
    val particleCount = 20
    val random = Random(42) // Fixed seed for consistent animation
    
    repeat(particleCount) { i ->
        val baseAngle = (i * 360f / particleCount)
        val animatedAngle = baseAngle + rotation * (0.3f + random.nextFloat() * 0.4f)
        val radians = Math.toRadians(animatedAngle.toDouble())
        
        val distance = 200f + random.nextFloat() * 300f
        val x = size.width / 2 + cos(radians).toFloat() * distance
        val y = size.height / 2 + sin(radians).toFloat() * distance
        
        val particleSize = 2f + random.nextFloat() * 3f
        val alpha = pulseAlpha * (0.4f + random.nextFloat() * 0.4f)
        
        drawCircle(
            color = SmartShieldColors.Secondary.copy(alpha = alpha),
            radius = particleSize,
            center = Offset(x, y)
        )
    }
}

private fun DrawScope.drawScanlines(
    scanlineOffset: Float,
    pulseAlpha: Float
) {
    val lineSpacing = 40f
    val lineCount = (size.height / lineSpacing).toInt() + 2
    val offsetY = scanlineOffset * lineSpacing
    
    repeat(lineCount) { i ->
        val y = (i * lineSpacing) - offsetY
        if (y >= -lineSpacing && y <= size.height + lineSpacing) {
            drawLine(
                color = SmartShieldColors.ScanningEffect.copy(alpha = pulseAlpha * 0.3f),
                start = Offset(0f, y),
                end = Offset(size.width, y),
                strokeWidth = 1f
            )
        }
    }
}
