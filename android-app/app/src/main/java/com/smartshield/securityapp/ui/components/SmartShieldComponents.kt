package com.smartshield.securityapp.ui.components

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.ripple.rememberRipple
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.smartshield.securityapp.ui.theme.SmartShieldColors
import kotlinx.coroutines.delay
import androidx.compose.foundation.layout.RowScope

// Professional Button Component
@Composable
fun SmartShieldButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    loading: Boolean = false,
    variant: ButtonVariant = ButtonVariant.Primary,
    size: ButtonSize = ButtonSize.Medium,
    icon: ImageVector? = null,
    iconPosition: IconPosition = IconPosition.Start
) {
    val buttonColors = when (variant) {
        ButtonVariant.Primary -> ButtonDefaults.buttonColors(
            containerColor = SmartShieldColors.Primary,
            contentColor = SmartShieldColors.OnPrimary,
            disabledContainerColor = SmartShieldColors.Disabled,
            disabledContentColor = SmartShieldColors.DisabledContent
        )
        ButtonVariant.Secondary -> ButtonDefaults.buttonColors(
            containerColor = SmartShieldColors.Secondary,
            contentColor = SmartShieldColors.OnSecondary,
            disabledContainerColor = SmartShieldColors.Disabled,
            disabledContentColor = SmartShieldColors.DisabledContent
        )
        ButtonVariant.Outline -> ButtonDefaults.outlinedButtonColors(
            contentColor = SmartShieldColors.Primary,
            disabledContentColor = SmartShieldColors.DisabledContent
        )
        ButtonVariant.Text -> ButtonDefaults.textButtonColors(
            contentColor = SmartShieldColors.Primary,
            disabledContentColor = SmartShieldColors.DisabledContent
        )
    }

    val buttonHeight = when (size) {
        ButtonSize.Small -> 36.dp
        ButtonSize.Medium -> 48.dp
        ButtonSize.Large -> 56.dp
    }

    val fontSize = when (size) {
        ButtonSize.Small -> 14.sp
        ButtonSize.Medium -> 16.sp
        ButtonSize.Large -> 18.sp
    }

    val horizontalPadding = when (size) {
        ButtonSize.Small -> 16.dp
        ButtonSize.Medium -> 24.dp
        ButtonSize.Large -> 32.dp
    }

    when (variant) {
        ButtonVariant.Primary, ButtonVariant.Secondary -> {
            Button(
                onClick = onClick,
                modifier = modifier
                    .height(buttonHeight)
                    .shadow(
                        elevation = if (enabled) 4.dp else 0.dp,
                        shape = RoundedCornerShape(12.dp)
                    ),
                enabled = enabled && !loading,
                colors = buttonColors,
                shape = RoundedCornerShape(12.dp),
                contentPadding = PaddingValues(horizontal = horizontalPadding, vertical = 0.dp)
            ) {
                ButtonContent(
                    text = text,
                    loading = loading,
                    icon = icon,
                    iconPosition = iconPosition,
                    fontSize = fontSize
                )
            }
        }
        ButtonVariant.Outline -> {
            OutlinedButton(
                onClick = onClick,
                modifier = modifier
                    .height(buttonHeight)
                    .border(
                        width = 1.dp,
                        color = if (enabled) SmartShieldColors.Primary else SmartShieldColors.Disabled,
                        shape = RoundedCornerShape(12.dp)
                    ),
                enabled = enabled && !loading,
                colors = buttonColors,
                shape = RoundedCornerShape(12.dp),
                contentPadding = PaddingValues(horizontal = horizontalPadding, vertical = 0.dp)
            ) {
                ButtonContent(
                    text = text,
                    loading = loading,
                    icon = icon,
                    iconPosition = iconPosition,
                    fontSize = fontSize
                )
            }
        }
        ButtonVariant.Text -> {
            TextButton(
                onClick = onClick,
                modifier = modifier.height(buttonHeight),
                enabled = enabled && !loading,
                colors = buttonColors,
                contentPadding = PaddingValues(horizontal = horizontalPadding, vertical = 0.dp)
            ) {
                ButtonContent(
                    text = text,
                    loading = loading,
                    icon = icon,
                    iconPosition = iconPosition,
                    fontSize = fontSize
                )
            }
        }
    }
}

@Composable
private fun ButtonContent(
    text: String,
    loading: Boolean,
    icon: ImageVector?,
    iconPosition: IconPosition,
    fontSize: androidx.compose.ui.unit.TextUnit
) {
    if (loading) {
        CircularProgressIndicator(
            modifier = Modifier.size(20.dp),
            color = SmartShieldColors.OnPrimary,
            strokeWidth = 2.dp
        )
    } else {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            if (icon != null && iconPosition == IconPosition.Start) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
            }
            
            Text(
                text = text,
                fontSize = fontSize,
                fontWeight = FontWeight.Medium
            )
            
            if (icon != null && iconPosition == IconPosition.End) {
                Spacer(modifier = Modifier.width(8.dp))
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    modifier = Modifier.size(20.dp)
                )
            }
        }
    }
}

// Professional Card Component
@Composable
fun SmartShieldCard(
    modifier: Modifier = Modifier,
    onClick: (() -> Unit)? = null,
    elevation: Dp = 4.dp,
    backgroundColor: Color = SmartShieldColors.CardBackground,
    borderColor: Color = SmartShieldColors.CardBorder,
    borderWidth: Dp = 0.dp,
    cornerRadius: Dp = 16.dp,
    content: @Composable ColumnScope.() -> Unit
) {
    val cardModifier = if (onClick != null) {
        modifier
            .shadow(
                elevation = elevation,
                shape = RoundedCornerShape(cornerRadius)
            )
            .clip(RoundedCornerShape(cornerRadius))
            .background(backgroundColor)
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = rememberRipple(color = SmartShieldColors.Primary)
            ) { onClick() }
            .then(
                if (borderWidth > 0.dp) {
                    Modifier.border(
                        width = borderWidth,
                        color = borderColor,
                        shape = RoundedCornerShape(cornerRadius)
                    )
                } else Modifier
            )
    } else {
        modifier
            .shadow(
                elevation = elevation,
                shape = RoundedCornerShape(cornerRadius)
            )
            .clip(RoundedCornerShape(cornerRadius))
            .background(backgroundColor)
            .then(
                if (borderWidth > 0.dp) {
                    Modifier.border(
                        width = borderWidth,
                        color = borderColor,
                        shape = RoundedCornerShape(cornerRadius)
                    )
                } else Modifier
            )
    }

    Column(
        modifier = cardModifier.padding(20.dp),
        content = content
    )
}

// Professional Status Badge
@Composable
fun StatusBadge(
    text: String,
    status: StatusType,
    modifier: Modifier = Modifier,
    size: BadgeSize = BadgeSize.Medium
) {
    val (backgroundColor, textColor) = when (status) {
        StatusType.Success -> SmartShieldColors.Success.copy(alpha = 0.1f) to SmartShieldColors.Success
        StatusType.Warning -> SmartShieldColors.Warning.copy(alpha = 0.1f) to SmartShieldColors.Warning
        StatusType.Error -> SmartShieldColors.Error.copy(alpha = 0.1f) to SmartShieldColors.Error
        StatusType.Info -> SmartShieldColors.Info.copy(alpha = 0.1f) to SmartShieldColors.Info
        StatusType.Neutral -> SmartShieldColors.Gray200 to SmartShieldColors.Gray700
    }

    val (fontSize, padding) = when (size) {
        BadgeSize.Small -> 10.sp to PaddingValues(horizontal = 8.dp, vertical = 4.dp)
        BadgeSize.Medium -> 12.sp to PaddingValues(horizontal = 12.dp, vertical = 6.dp)
        BadgeSize.Large -> 14.sp to PaddingValues(horizontal = 16.dp, vertical = 8.dp)
    }

    Box(
        modifier = modifier
            .background(
                color = backgroundColor,
                shape = RoundedCornerShape(20.dp)
            )
            .padding(padding),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = text,
            color = textColor,
            fontSize = fontSize,
            fontWeight = FontWeight.Medium,
            textAlign = TextAlign.Center
        )
    }
}

// Enhanced Compact Language Item Component
@Composable
fun EnhancedCompactLanguageItem(
    language: Language,
    isSelected: Boolean,
    onSelect: () -> Unit,
    animationDelay: Int = 0,
    modifier: Modifier = Modifier
) {
    var isVisible by remember { mutableStateOf(false) }

    LaunchedEffect(Unit) {
        kotlinx.coroutines.delay(animationDelay.toLong())
        isVisible = true
    }

    AnimatedVisibility(
        visible = isVisible,
        enter = slideInHorizontally(
            initialOffsetX = { it / 2 },
            animationSpec = spring(
                dampingRatio = Spring.DampingRatioMediumBouncy,
                stiffness = Spring.StiffnessMedium
            )
        ) + fadeIn(animationSpec = tween(300))
    ) {
        SmartShieldCard(
            modifier = modifier
                .fillMaxWidth()
                .height(80.dp),
            onClick = onSelect,
            elevation = if (isSelected) 8.dp else 2.dp,
            backgroundColor = if (isSelected) {
                SmartShieldColors.Primary.copy(alpha = 0.1f)
            } else {
                SmartShieldColors.Surface
            },
            borderColor = if (isSelected) {
                SmartShieldColors.Primary.copy(alpha = 0.5f)
            } else {
                SmartShieldColors.CardBorder
            },
            borderWidth = if (isSelected) 2.dp else 1.dp,
            cornerRadius = 12.dp
        ) {
            Row(
                modifier = Modifier.fillMaxSize(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = language.flagEmoji,
                        fontSize = 24.sp
                    )

                    Spacer(modifier = Modifier.width(12.dp))

                    Column {
                        Text(
                            text = language.nativeName,
                            style = MaterialTheme.typography.titleSmall,
                            color = SmartShieldColors.OnSurface,
                            fontWeight = FontWeight.Bold
                        )
                        Text(
                            text = language.name,
                            style = MaterialTheme.typography.bodySmall,
                            color = SmartShieldColors.OnSurfaceVariant
                        )
                    }
                }

                if (isSelected) {
                    Icon(
                        imageVector = androidx.compose.material.icons.Icons.Default.Check,
                        contentDescription = "Selected",
                        tint = SmartShieldColors.Primary,
                        modifier = Modifier.size(20.dp)
                    )
                }
            }
        }
    }
}

// Language data class
data class Language(
    val code: String,
    val name: String,
    val nativeName: String,
    val flagEmoji: String,
    val isPopular: Boolean = false,
    val isRtl: Boolean = false,
    val region: String = ""
)

// Enums for component variants
enum class ButtonVariant { Primary, Secondary, Outline, Text }
enum class ButtonSize { Small, Medium, Large }
enum class IconPosition { Start, End }
enum class StatusType { Success, Warning, Error, Info, Neutral }
enum class BadgeSize { Small, Medium, Large }
