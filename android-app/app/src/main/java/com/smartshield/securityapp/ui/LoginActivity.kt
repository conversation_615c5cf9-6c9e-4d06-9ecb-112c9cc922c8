package com.smartshield.securityapp.ui

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Email
import androidx.compose.material.icons.filled.Lock
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Visibility
import androidx.compose.material.icons.filled.VisibilityOff
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shadow
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import com.smartshield.securityapp.R
import com.smartshield.securityapp.network.NetworkModule
import com.smartshield.securityapp.network.NetworkResult
import com.smartshield.securityapp.repository.AuthRepository
import com.smartshield.securityapp.services.BiometricAuthService
import com.smartshield.securityapp.services.GoogleAuthService
import com.smartshield.securityapp.services.PasswordResetService
import com.smartshield.securityapp.ui.components.*
import com.smartshield.securityapp.ui.theme.SmartShieldColors
import com.smartshield.securityapp.ui.theme.SmartShieldTheme
import com.smartshield.securityapp.utils.TokenManager
import com.smartshield.securityapp.viewmodels.AuthViewModel
import com.smartshield.securityapp.viewmodels.AuthViewModelFactory
import timber.log.Timber

class LoginActivity : BaseActivity() {

    private var googleAuthService: GoogleAuthService? = null
    private var biometricAuthService: BiometricAuthService? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        try {
            // Initialize services safely
            googleAuthService = GoogleAuthService(this)
            biometricAuthService = BiometricAuthService(this)

            setContent {
                SmartShieldTheme {
                    AdvancedLoginScreen(
                        googleAuthService = googleAuthService,
                        biometricAuthService = biometricAuthService,
                        onLoginSuccess = {
                            navigateToDashboard()
                        }
                    )
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "Error setting content")
            // Fallback to enhanced layout
            setContent {
                SmartShieldTheme {
                    EnhancedLoginScreen(
                        googleAuthService = googleAuthService,
                        onLoginSuccess = { navigateToDashboard() }
                    )
                }
            }
        }
    }

    private fun navigateToDashboard() {
        try {
            // Save login state
            val sharedPrefs = getSharedPreferences("smart_shield_prefs", Context.MODE_PRIVATE)
            sharedPrefs.edit()
                .putBoolean("is_logged_in", true)
                .apply()

            val intent = Intent(this, DashboardActivity::class.java)
            startActivity(intent)
            finish()
        } catch (e: Exception) {
            Timber.e(e, "Error navigating to dashboard")
        }
    }
}

@Composable
fun AdvancedLoginScreen(
    googleAuthService: GoogleAuthService?,
    biometricAuthService: BiometricAuthService?,
    onLoginSuccess: () -> Unit
) {
    var isSignIn by remember { mutableStateOf(true) }
    var email by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    var confirmPassword by remember { mutableStateOf("") }
    var fullName by remember { mutableStateOf("") }
    var passwordVisible by remember { mutableStateOf(false) }
    var confirmPasswordVisible by remember { mutableStateOf(false) }
    var isLoading by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf("") }
    var successMessage by remember { mutableStateOf("") }
    var showPasswordReset by remember { mutableStateOf(false) }
    var showBiometricPrompt by remember { mutableStateOf(false) }
    var isBiometricAvailable by remember { mutableStateOf(false) }

    val context = LocalContext.current

    // Check biometric availability
    LaunchedEffect(Unit) {
        try {
            biometricAuthService?.let { service ->
                isBiometricAvailable = service.checkBiometricCapability() == BiometricAuthService.BiometricCapability.AVAILABLE
                Timber.d("Biometric available: $isBiometricAvailable")
            }
        } catch (e: Exception) {
            Timber.e(e, "Error checking biometric availability")
        }
    }

    // Network connectivity state
    var isNetworkAvailable by remember { mutableStateOf(true) }

    // Check network connectivity
    LaunchedEffect(Unit) {
        try {
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            val network = connectivityManager.activeNetwork
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            isNetworkAvailable = capabilities?.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) == true
            Timber.d("Network available: $isNetworkAvailable")
        } catch (e: Exception) {
            Timber.e(e, "Error checking network connectivity")
            isNetworkAvailable = true // Assume available if check fails
        }
    }

    // Initialize network components with proper error handling
    val tokenManager = remember {
        try {
            TokenManager(context)
        } catch (e: Exception) {
            Timber.e(e, "Error initializing TokenManager")
            null
        }
    }

    val apiService = remember {
        try {
            NetworkModule.provideApiService(context)
        } catch (e: Exception) {
            Timber.e(e, "Error initializing ApiService")
            null
        }
    }

    val authRepository = remember {
        try {
            if (tokenManager != null && apiService != null) {
                AuthRepository(apiService, tokenManager, context)
            } else {
                Timber.w("TokenManager or ApiService is null, using fallback")
                null
            }
        } catch (e: Exception) {
            Timber.e(e, "Error initializing AuthRepository")
            null
        }
    }

    val authViewModel: AuthViewModel? = try {
        if (authRepository != null) {
            viewModel(factory = AuthViewModelFactory(authRepository))
        } else {
            Timber.w("AuthRepository is null, ViewModel not available")
            null
        }
    } catch (e: Exception) {
        Timber.e(e, "Error initializing AuthViewModel")
        null
    }

    // Observe authentication states with null safety
    val loginState by (authViewModel?.loginState?.collectAsState() ?: remember { mutableStateOf(null) })
    val registerState by (authViewModel?.registerState?.collectAsState() ?: remember { mutableStateOf(null) })
    val isLoggedIn by (authViewModel?.isLoggedIn?.collectAsState() ?: remember { mutableStateOf(false) })

    // Enhanced authentication result handling
    LaunchedEffect(loginState) {
        try {
            val currentLoginState = loginState
            when (currentLoginState) {
                is NetworkResult.Success -> {
                    if (currentLoginState.data.success) {
                        successMessage = context.getString(R.string.welcome_back)
                        isLoading = false

                        // Clear any previous errors
                        errorMessage = ""

                        // Log successful login
                        Timber.d("Login successful, navigating to dashboard")

                        // Navigate after a brief delay to show success message
                        kotlinx.coroutines.delay(1000)
                        onLoginSuccess()
                    } else {
                        // Handle unsuccessful response
                        errorMessage = currentLoginState.data.message ?: context.getString(R.string.authentication_failed)
                        isLoading = false
                        Timber.w("Login response unsuccessful: ${currentLoginState.data.message}")
                    }
                }
                is NetworkResult.Error -> {
                    errorMessage = when {
                        currentLoginState.message.contains("network", ignoreCase = true) ->
                            "Network error. Please check your connection."
                        currentLoginState.message.contains("timeout", ignoreCase = true) ->
                            "Request timed out. Please try again."
                        currentLoginState.code == 401 ->
                            "Invalid email or password."
                        currentLoginState.code == 429 ->
                            "Too many attempts. Please try again later."
                        else ->
                            currentLoginState.message ?: context.getString(R.string.authentication_failed)
                    }
                    isLoading = false
                    successMessage = ""
                    Timber.e("Login failed: ${currentLoginState.message}")
                }
                is NetworkResult.Loading -> {
                    isLoading = currentLoginState.isLoading
                    if (currentLoginState.isLoading) {
                        errorMessage = ""
                        successMessage = ""
                    }
                }
                null -> {
                    // Initial state or cleared state
                    isLoading = false
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "Error handling login state")
            errorMessage = "An unexpected error occurred"
            isLoading = false
        }
    }

    LaunchedEffect(registerState) {
        try {
            val currentRegisterState = registerState
            when (currentRegisterState) {
                is NetworkResult.Success -> {
                    if (currentRegisterState.data.success) {
                        successMessage = context.getString(R.string.account_created_successfully)
                        isLoading = false

                        // Clear any previous errors
                        errorMessage = ""

                        // Log successful registration
                        Timber.d("Registration successful, navigating to dashboard")

                        // Navigate after a brief delay to show success message
                        kotlinx.coroutines.delay(1000)
                        onLoginSuccess()
                    } else {
                        // Handle unsuccessful response
                        errorMessage = currentRegisterState.data.message ?: context.getString(R.string.authentication_failed)
                        isLoading = false
                        Timber.w("Registration response unsuccessful: ${currentRegisterState.data.message}")
                    }
                }
                is NetworkResult.Error -> {
                    errorMessage = when {
                        currentRegisterState.message.contains("email", ignoreCase = true) &&
                        currentRegisterState.message.contains("exists", ignoreCase = true) ->
                            "Email already exists. Please use a different email or try signing in."
                        currentRegisterState.message.contains("network", ignoreCase = true) ->
                            "Network error. Please check your connection."
                        currentRegisterState.message.contains("timeout", ignoreCase = true) ->
                            "Request timed out. Please try again."
                        currentRegisterState.code == 400 ->
                            "Invalid registration data. Please check your information."
                        else ->
                            currentRegisterState.message ?: context.getString(R.string.authentication_failed)
                    }
                    isLoading = false
                    successMessage = ""
                    Timber.e("Registration failed: ${currentRegisterState.message}")
                }
                is NetworkResult.Loading -> {
                    isLoading = currentRegisterState.isLoading
                    if (currentRegisterState.isLoading) {
                        errorMessage = ""
                        successMessage = ""
                    }
                }
                null -> {
                    // Initial state or cleared state
                    isLoading = false
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "Error handling register state")
            errorMessage = "An unexpected error occurred"
            isLoading = false
        }
    }

    // Handle automatic login status check
    LaunchedEffect(isLoggedIn) {
        if (isLoggedIn) {
            Timber.d("User already logged in, navigating to dashboard")
            onLoginSuccess()
        }
    }

    // Initialize Google Auth with enhanced error handling
    LaunchedEffect(Unit) {
        try {
            googleAuthService?.let { service ->
                if (context is androidx.appcompat.app.AppCompatActivity) {
                    service.initialize(context) { result ->
                        if (result.success && result.idToken != null) {
                            Timber.d("Google Sign-In successful, authenticating with backend")
                            authViewModel?.googleAuth(result.idToken) ?: run {
                                errorMessage = "Authentication service not available"
                                isLoading = false
                            }
                        } else {
                            val errorMsg = when {
                                result.error?.contains("cancelled", ignoreCase = true) == true ->
                                    "Google Sign-In was cancelled"
                                result.error?.contains("network", ignoreCase = true) == true ->
                                    "Network error during Google Sign-In"
                                result.error?.contains("developer_error", ignoreCase = true) == true ->
                                    "Google Sign-In configuration error"
                                else ->
                                    result.error ?: context.getString(R.string.google_sign_in_failed)
                            }
                            errorMessage = errorMsg
                            isLoading = false
                            Timber.e("Google Sign-In failed: ${result.error}")
                        }
                    }
                } else {
                    Timber.e("Context is not AppCompatActivity, cannot initialize Google Auth")
                }
            } ?: run {
                Timber.w("GoogleAuthService is null, Google Sign-In not available")
            }
        } catch (e: Exception) {
            Timber.e(e, "Error initializing Google Auth")
            errorMessage = "Failed to initialize Google Sign-In"
        }
    }

    Box(
        modifier = Modifier.fillMaxSize()
    ) {
        // Advanced Cyberpunk animated background
        try {
            CyberpunkBackground(
                modifier = Modifier.fillMaxSize(),
                showNetworkLines = true,
                showParticles = true,
                showGlowEffects = true
            )

            // Add holographic data stream effect
            HolographicDataStream(
                isActive = isLoading,
                modifier = Modifier.fillMaxSize()
            )
        } catch (e: Exception) {
            Timber.e(e, "Error with CyberpunkBackground, using fallback")
            // Fallback background
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(
                        brush = Brush.verticalGradient(
                            colors = listOf(
                                Color(0xFF0A0A0A),
                                Color(0xFF1A1A2E),
                                Color(0xFF16213E)
                            )
                        )
                    )
            )
        }

        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {

            // Enhanced Cyberpunk Logo and Title with glitch effect
            GlitchEffect(
                isActive = isLoading
            ) {
                Box(
                    modifier = Modifier
                        .size(120.dp)
                        .background(
                            brush = Brush.radialGradient(
                                colors = listOf(
                                    SmartShieldColors.Primary.copy(alpha = 0.3f),
                                    SmartShieldColors.PrimaryGlow,
                                    Color.Transparent
                                ),
                                radius = 200f
                            ),
                            shape = RoundedCornerShape(60.dp)
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "🛡️",
                        fontSize = 56.sp,
                        modifier = Modifier
                            .background(
                                brush = Brush.radialGradient(
                                    colors = listOf(
                                        SmartShieldColors.GlowEffect,
                                        Color.Transparent
                                    ),
                                    radius = 100f
                                ),
                                shape = RoundedCornerShape(40.dp)
                            )
                            .padding(12.dp)
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = stringResource(R.string.smart_shield),
                fontSize = 28.sp,
                fontWeight = FontWeight.Bold,
                color = SmartShieldColors.Primary,
                textAlign = TextAlign.Center,
                style = androidx.compose.ui.text.TextStyle(
                    shadow = Shadow(
                        color = SmartShieldColors.PrimaryGlow,
                        offset = Offset(0f, 0f),
                        blurRadius = 20f
                    )
                )
            )

            Spacer(modifier = Modifier.height(32.dp))

            // Enhanced Toggle with cyberpunk styling
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        SmartShieldColors.CardBackground.copy(alpha = 0.3f),
                        RoundedCornerShape(12.dp)
                    )
                    .padding(4.dp),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                CyberpunkButton(
                    onClick = {
                        isSignIn = true
                        errorMessage = ""
                        successMessage = ""
                    },
                    modifier = Modifier
                        .weight(1f)
                        .height(48.dp),
                    enabled = !isLoading
                ) {
                    Text(
                        text = stringResource(R.string.sign_in),
                        fontWeight = FontWeight.Medium,
                        fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace,
                        color = if (isSignIn) SmartShieldColors.OnPrimary else SmartShieldColors.Primary
                    )
                }

                Spacer(modifier = Modifier.width(8.dp))

                CyberpunkButton(
                    onClick = {
                        isSignIn = false
                        errorMessage = ""
                        successMessage = ""
                    },
                    modifier = Modifier
                        .weight(1f)
                        .height(48.dp),
                    enabled = !isLoading
                ) {
                    Text(
                        text = stringResource(R.string.create_account),
                        fontWeight = FontWeight.Medium,
                        fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace,
                        color = if (!isSignIn) SmartShieldColors.OnPrimary else SmartShieldColors.Primary
                    )
                }
            }

            Spacer(modifier = Modifier.height(24.dp))
        }
    }
}

@Composable
fun EnhancedLoginScreen(
    googleAuthService: GoogleAuthService?,
    onLoginSuccess: () -> Unit
) {
    var isSignIn by remember { mutableStateOf(true) }
    var email by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    var confirmPassword by remember { mutableStateOf("") }
    var fullName by remember { mutableStateOf("") }
    var passwordVisible by remember { mutableStateOf(false) }
    var confirmPasswordVisible by remember { mutableStateOf(false) }
    var isLoading by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf("") }
    var successMessage by remember { mutableStateOf("") }

    val context = LocalContext.current

    // Network connectivity state
    var isNetworkAvailable by remember { mutableStateOf(true) }

    // Check network connectivity
    LaunchedEffect(Unit) {
        try {
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            val network = connectivityManager.activeNetwork
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            isNetworkAvailable = capabilities?.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) == true
            Timber.d("Network available: $isNetworkAvailable")
        } catch (e: Exception) {
            Timber.e(e, "Error checking network connectivity")
            isNetworkAvailable = true // Assume available if check fails
        }
    }

    // Initialize network components with proper error handling
    val tokenManager = remember {
        try {
            TokenManager(context)
        } catch (e: Exception) {
            Timber.e(e, "Error initializing TokenManager")
            null
        }
    }

    val apiService = remember {
        try {
            NetworkModule.provideApiService(context)
        } catch (e: Exception) {
            Timber.e(e, "Error initializing ApiService")
            null
        }
    }

    val authRepository = remember {
        try {
            if (tokenManager != null && apiService != null) {
                AuthRepository(apiService, tokenManager, context)
            } else {
                Timber.w("TokenManager or ApiService is null, using fallback")
                null
            }
        } catch (e: Exception) {
            Timber.e(e, "Error initializing AuthRepository")
            null
        }
    }

    val authViewModel: AuthViewModel? = try {
        if (authRepository != null) {
            viewModel(factory = AuthViewModelFactory(authRepository))
        } else {
            Timber.w("AuthRepository is null, ViewModel not available")
            null
        }
    } catch (e: Exception) {
        Timber.e(e, "Error initializing AuthViewModel")
        null
    }

    // Observe authentication states with null safety
    val loginState by (authViewModel?.loginState?.collectAsState() ?: remember { mutableStateOf(null) })
    val registerState by (authViewModel?.registerState?.collectAsState() ?: remember { mutableStateOf(null) })
    val isLoggedIn by (authViewModel?.isLoggedIn?.collectAsState() ?: remember { mutableStateOf(false) })

    // Enhanced authentication result handling
    LaunchedEffect(loginState) {
        try {
            val currentLoginState = loginState
            when (currentLoginState) {
                is NetworkResult.Success -> {
                    if (currentLoginState.data.success) {
                        successMessage = context.getString(R.string.welcome_back)
                        isLoading = false

                        // Clear any previous errors
                        errorMessage = ""

                        // Log successful login
                        Timber.d("Login successful, navigating to dashboard")

                        // Navigate after a brief delay to show success message
                        kotlinx.coroutines.delay(1000)
                        onLoginSuccess()
                    } else {
                        // Handle unsuccessful response
                        errorMessage = currentLoginState.data.message ?: context.getString(R.string.authentication_failed)
                        isLoading = false
                        Timber.w("Login response unsuccessful: ${currentLoginState.data.message}")
                    }
                }
                is NetworkResult.Error -> {
                    errorMessage = when {
                        currentLoginState.message.contains("network", ignoreCase = true) ->
                            "Network error. Please check your connection."
                        currentLoginState.message.contains("timeout", ignoreCase = true) ->
                            "Request timed out. Please try again."
                        currentLoginState.code == 401 ->
                            "Invalid email or password."
                        currentLoginState.code == 429 ->
                            "Too many attempts. Please try again later."
                        else ->
                            currentLoginState.message ?: context.getString(R.string.authentication_failed)
                    }
                    isLoading = false
                    successMessage = ""
                    Timber.e("Login failed: ${currentLoginState.message}")
                }
                is NetworkResult.Loading -> {
                    isLoading = currentLoginState.isLoading
                    if (currentLoginState.isLoading) {
                        errorMessage = ""
                        successMessage = ""
                    }
                }
                null -> {
                    // Initial state or cleared state
                    isLoading = false
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "Error handling login state")
            errorMessage = "An unexpected error occurred"
            isLoading = false
        }
    }

    LaunchedEffect(registerState) {
        try {
            val currentRegisterState = registerState
            when (currentRegisterState) {
                is NetworkResult.Success -> {
                    if (currentRegisterState.data.success) {
                        successMessage = context.getString(R.string.account_created_successfully)
                        isLoading = false

                        // Clear any previous errors
                        errorMessage = ""

                        // Log successful registration
                        Timber.d("Registration successful, navigating to dashboard")

                        // Navigate after a brief delay to show success message
                        kotlinx.coroutines.delay(1000)
                        onLoginSuccess()
                    } else {
                        // Handle unsuccessful response
                        errorMessage = currentRegisterState.data.message ?: context.getString(R.string.authentication_failed)
                        isLoading = false
                        Timber.w("Registration response unsuccessful: ${currentRegisterState.data.message}")
                    }
                }
                is NetworkResult.Error -> {
                    errorMessage = when {
                        currentRegisterState.message.contains("email", ignoreCase = true) &&
                        currentRegisterState.message.contains("exists", ignoreCase = true) ->
                            "Email already exists. Please use a different email or try signing in."
                        currentRegisterState.message.contains("network", ignoreCase = true) ->
                            "Network error. Please check your connection."
                        currentRegisterState.message.contains("timeout", ignoreCase = true) ->
                            "Request timed out. Please try again."
                        currentRegisterState.code == 400 ->
                            "Invalid registration data. Please check your information."
                        else ->
                            currentRegisterState.message ?: context.getString(R.string.authentication_failed)
                    }
                    isLoading = false
                    successMessage = ""
                    Timber.e("Registration failed: ${currentRegisterState.message}")
                }
                is NetworkResult.Loading -> {
                    isLoading = currentRegisterState.isLoading
                    if (currentRegisterState.isLoading) {
                        errorMessage = ""
                        successMessage = ""
                    }
                }
                null -> {
                    // Initial state or cleared state
                    isLoading = false
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "Error handling register state")
            errorMessage = "An unexpected error occurred"
            isLoading = false
        }
    }

    // Handle automatic login status check
    LaunchedEffect(isLoggedIn) {
        if (isLoggedIn) {
            Timber.d("User already logged in, navigating to dashboard")
            onLoginSuccess()
        }
    }

    // Initialize Google Auth with enhanced error handling
    LaunchedEffect(Unit) {
        try {
            googleAuthService?.let { service ->
                if (context is androidx.appcompat.app.AppCompatActivity) {
                    service.initialize(context) { result ->
                        if (result.success && result.idToken != null) {
                            Timber.d("Google Sign-In successful, authenticating with backend")
                            authViewModel?.googleAuth(result.idToken) ?: run {
                                errorMessage = "Authentication service not available"
                                isLoading = false
                            }
                        } else {
                            val errorMsg = when {
                                result.error?.contains("cancelled", ignoreCase = true) == true ->
                                    "Google Sign-In was cancelled"
                                result.error?.contains("network", ignoreCase = true) == true ->
                                    "Network error during Google Sign-In"
                                result.error?.contains("developer_error", ignoreCase = true) == true ->
                                    "Google Sign-In configuration error"
                                else ->
                                    result.error ?: context.getString(R.string.google_sign_in_failed)
                            }
                            errorMessage = errorMsg
                            isLoading = false
                            Timber.e("Google Sign-In failed: ${result.error}")
                        }
                    }
                } else {
                    Timber.e("Context is not AppCompatActivity, cannot initialize Google Auth")
                }
            } ?: run {
                Timber.w("GoogleAuthService is null, Google Sign-In not available")
            }
        } catch (e: Exception) {
            Timber.e(e, "Error initializing Google Auth")
            errorMessage = "Failed to initialize Google Sign-In"
        }
    }

    Box(
        modifier = Modifier.fillMaxSize()
    ) {
        // Cyberpunk animated background
        try {
            CyberpunkBackground(
                modifier = Modifier.fillMaxSize(),
                showNetworkLines = true,
                showParticles = true,
                showGlowEffects = true
            )
        } catch (e: Exception) {
            Timber.e(e, "Error with CyberpunkBackground, using fallback")
            // Fallback background
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(
                        brush = Brush.verticalGradient(
                            colors = listOf(
                                Color(0xFF0A0A0A),
                                Color(0xFF1A1A2E),
                                Color(0xFF16213E)
                            )
                        )
                    )
            )
        }

        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {

            // Cyberpunk Logo and Title
            Box(
                modifier = Modifier
                    .size(120.dp)
                    .background(
                        brush = Brush.radialGradient(
                            colors = listOf(
                                SmartShieldColors.Primary.copy(alpha = 0.3f),
                                SmartShieldColors.PrimaryGlow,
                                Color.Transparent
                            ),
                            radius = 200f
                        ),
                        shape = RoundedCornerShape(60.dp)
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "🛡️",
                    fontSize = 56.sp,
                    modifier = Modifier
                        .background(
                            brush = Brush.radialGradient(
                                colors = listOf(
                                    SmartShieldColors.GlowEffect,
                                    Color.Transparent
                                ),
                                radius = 100f
                            ),
                            shape = RoundedCornerShape(40.dp)
                        )
                        .padding(12.dp)
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = stringResource(R.string.smart_shield),
                fontSize = 28.sp,
                fontWeight = FontWeight.Bold,
                color = SmartShieldColors.Primary,
                textAlign = TextAlign.Center,
                style = androidx.compose.ui.text.TextStyle(
                    shadow = Shadow(
                        color = SmartShieldColors.PrimaryGlow,
                        offset = Offset(0f, 0f),
                        blurRadius = 20f
                    )
                )
            )

            Spacer(modifier = Modifier.height(32.dp))

            // Cyberpunk Toggle
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        SmartShieldColors.CardBackground.copy(alpha = 0.3f),
                        RoundedCornerShape(12.dp)
                    )
                    .padding(4.dp),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                Button(
                    onClick = {
                        isSignIn = true
                        errorMessage = ""
                        successMessage = ""
                    },
                    modifier = Modifier
                        .weight(1f)
                        .height(48.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = if (isSignIn) SmartShieldColors.Primary else Color.Transparent,
                        contentColor = if (isSignIn) SmartShieldColors.OnPrimary else SmartShieldColors.Primary
                    ),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Text(
                        text = stringResource(R.string.sign_in),
                        fontWeight = FontWeight.Medium,
                        fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace
                    )
                }

                Spacer(modifier = Modifier.width(8.dp))

                Button(
                    onClick = {
                        isSignIn = false
                        errorMessage = ""
                        successMessage = ""
                    },
                    modifier = Modifier
                        .weight(1f)
                        .height(48.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = if (!isSignIn) SmartShieldColors.Primary else Color.Transparent,
                        contentColor = if (!isSignIn) SmartShieldColors.OnPrimary else SmartShieldColors.Primary
                    ),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Text(
                        text = stringResource(R.string.create_account),
                        fontWeight = FontWeight.Medium,
                        fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace
                    )
                }
            }

            Spacer(modifier = Modifier.height(24.dp))

            // Cyberpunk Login Form
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(16.dp)),
                colors = CardDefaults.cardColors(
                    containerColor = SmartShieldColors.CardBackground.copy(alpha = 0.9f)
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
                border = BorderStroke(
                    1.dp,
                    SmartShieldColors.Primary.copy(alpha = 0.3f)
                )
            ) {
                Column(
                    modifier = Modifier.padding(24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {

                    Text(
                        text = if (isSignIn) stringResource(R.string.welcome_to) else stringResource(R.string.create_account),
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold,
                        color = SmartShieldColors.Primary,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth(),
                        fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = if (isSignIn)
                            stringResource(R.string.please_enter_credentials)
                        else
                            stringResource(R.string.please_enter_details),
                        fontSize = 14.sp,
                        color = SmartShieldColors.OnSurfaceVariant,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth()
                    )

                    Spacer(modifier = Modifier.height(24.dp))

                    // Network Status Indicator
                    if (!isNetworkAvailable) {
                        Card(
                            modifier = Modifier.fillMaxWidth(),
                            colors = CardDefaults.cardColors(
                                containerColor = Color(0xFFFF9800).copy(alpha = 0.1f)
                            ),
                            border = BorderStroke(
                                1.dp,
                                Color(0xFFFF9800).copy(alpha = 0.3f)
                            )
                        ) {
                            Row(
                                modifier = Modifier.padding(12.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = "⚠️",
                                    fontSize = 16.sp,
                                    modifier = Modifier.padding(end = 8.dp)
                                )
                                Text(
                                    text = "No internet connection. Using offline mode.",
                                    color = Color(0xFFFF9800),
                                    fontSize = 14.sp
                                )
                            }
                        }
                        Spacer(modifier = Modifier.height(16.dp))
                    }

                    // Success Message
                    if (successMessage.isNotEmpty()) {
                        Card(
                            modifier = Modifier.fillMaxWidth(),
                            colors = CardDefaults.cardColors(
                                containerColor = SmartShieldColors.Primary.copy(alpha = 0.1f)
                            ),
                            border = BorderStroke(
                                1.dp,
                                SmartShieldColors.Primary.copy(alpha = 0.3f)
                            )
                        ) {
                            Text(
                                text = successMessage,
                                modifier = Modifier.padding(12.dp),
                                color = SmartShieldColors.Primary,
                                fontSize = 14.sp
                            )
                        }
                        Spacer(modifier = Modifier.height(16.dp))
                    }

                    // Error Message
                    if (errorMessage.isNotEmpty()) {
                        Card(
                            modifier = Modifier.fillMaxWidth(),
                            colors = CardDefaults.cardColors(
                                containerColor = SmartShieldColors.Error.copy(alpha = 0.1f)
                            ),
                            border = BorderStroke(
                                1.dp,
                                SmartShieldColors.Error.copy(alpha = 0.3f)
                            )
                        ) {
                            Text(
                                text = errorMessage,
                                modifier = Modifier.padding(12.dp),
                                color = SmartShieldColors.Error,
                                fontSize = 14.sp
                            )
                        }
                        Spacer(modifier = Modifier.height(16.dp))
                    }

                    // Full Name Field (only for sign up)
                    if (!isSignIn) {
                        OutlinedTextField(
                            value = fullName,
                            onValueChange = { fullName = it },
                            label = {
                                Text(
                                    stringResource(R.string.full_name),
                                    fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace
                                )
                            },
                            leadingIcon = {
                                Icon(
                                    imageVector = Icons.Default.Person,
                                    contentDescription = "Full Name",
                                    tint = SmartShieldColors.Primary
                                )
                            },
                            modifier = Modifier.fillMaxWidth(),
                            singleLine = true,
                            enabled = !isLoading,
                            colors = OutlinedTextFieldDefaults.colors(
                                focusedBorderColor = SmartShieldColors.Primary,
                                unfocusedBorderColor = SmartShieldColors.Primary.copy(alpha = 0.3f),
                                focusedLabelColor = SmartShieldColors.Primary,
                                unfocusedLabelColor = SmartShieldColors.OnSurfaceVariant,
                                cursorColor = SmartShieldColors.Primary,
                                focusedTextColor = SmartShieldColors.OnSurface,
                                unfocusedTextColor = SmartShieldColors.OnSurfaceVariant
                            )
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                    }

                    // Email Field
                    OutlinedTextField(
                        value = email,
                        onValueChange = { email = it },
                        label = {
                            Text(
                                stringResource(R.string.email),
                                fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace
                            )
                        },
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Default.Email,
                                contentDescription = "Email",
                                tint = SmartShieldColors.Primary
                            )
                        },
                        keyboardOptions = KeyboardOptions(
                            keyboardType = KeyboardType.Email
                        ),
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true,
                        enabled = !isLoading,
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = SmartShieldColors.Primary,
                            unfocusedBorderColor = SmartShieldColors.Primary.copy(alpha = 0.3f),
                            focusedLabelColor = SmartShieldColors.Primary,
                            unfocusedLabelColor = SmartShieldColors.OnSurfaceVariant,
                            cursorColor = SmartShieldColors.Primary,
                            focusedTextColor = SmartShieldColors.OnSurface,
                            unfocusedTextColor = SmartShieldColors.OnSurfaceVariant
                        )
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Password Field
                    OutlinedTextField(
                        value = password,
                        onValueChange = { password = it },
                        label = {
                            Text(
                                stringResource(R.string.password),
                                fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace
                            )
                        },
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Default.Lock,
                                contentDescription = "Password",
                                tint = SmartShieldColors.Primary
                            )
                        },
                        trailingIcon = {
                            IconButton(
                                onClick = { passwordVisible = !passwordVisible }
                            ) {
                                Icon(
                                    imageVector = if (passwordVisible) Icons.Default.Visibility else Icons.Default.VisibilityOff,
                                    contentDescription = if (passwordVisible)
                                        stringResource(R.string.hide_password)
                                    else
                                        stringResource(R.string.show_password),
                                    tint = SmartShieldColors.Primary.copy(alpha = 0.7f)
                                )
                            }
                        },
                        visualTransformation = if (passwordVisible)
                            VisualTransformation.None
                        else
                            PasswordVisualTransformation(),
                        keyboardOptions = KeyboardOptions(
                            keyboardType = KeyboardType.Password
                        ),
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true,
                        enabled = !isLoading,
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = SmartShieldColors.Primary,
                            unfocusedBorderColor = SmartShieldColors.Primary.copy(alpha = 0.3f),
                            focusedLabelColor = SmartShieldColors.Primary,
                            unfocusedLabelColor = SmartShieldColors.OnSurfaceVariant,
                            cursorColor = SmartShieldColors.Primary,
                            focusedTextColor = SmartShieldColors.OnSurface,
                            unfocusedTextColor = SmartShieldColors.OnSurfaceVariant
                        )
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Confirm Password Field (only for sign up)
                    if (!isSignIn) {
                        OutlinedTextField(
                            value = confirmPassword,
                            onValueChange = { confirmPassword = it },
                            label = {
                                Text(
                                    stringResource(R.string.confirm_password),
                                    fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace
                                )
                            },
                            leadingIcon = {
                                Icon(
                                    imageVector = Icons.Default.Lock,
                                    contentDescription = "Confirm Password",
                                    tint = SmartShieldColors.Primary
                                )
                            },
                            trailingIcon = {
                                IconButton(
                                    onClick = { confirmPasswordVisible = !confirmPasswordVisible }
                                ) {
                                    Icon(
                                        imageVector = if (confirmPasswordVisible) Icons.Default.Visibility else Icons.Default.VisibilityOff,
                                        contentDescription = if (confirmPasswordVisible)
                                            stringResource(R.string.hide_password)
                                        else
                                            stringResource(R.string.show_password),
                                        tint = SmartShieldColors.Primary.copy(alpha = 0.7f)
                                    )
                                }
                            },
                            visualTransformation = if (confirmPasswordVisible)
                                VisualTransformation.None
                            else
                                PasswordVisualTransformation(),
                            keyboardOptions = KeyboardOptions(
                                keyboardType = KeyboardType.Password
                            ),
                            modifier = Modifier.fillMaxWidth(),
                            singleLine = true,
                            enabled = !isLoading,
                            colors = OutlinedTextFieldDefaults.colors(
                                focusedBorderColor = SmartShieldColors.Primary,
                                unfocusedBorderColor = SmartShieldColors.Primary.copy(alpha = 0.3f),
                                focusedLabelColor = SmartShieldColors.Primary,
                                unfocusedLabelColor = SmartShieldColors.OnSurfaceVariant,
                                cursorColor = SmartShieldColors.Primary,
                                focusedTextColor = SmartShieldColors.OnSurface,
                                unfocusedTextColor = SmartShieldColors.OnSurfaceVariant
                            )
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                    }

                    Spacer(modifier = Modifier.height(24.dp))

                    // Enhanced Main Action Button with Network Integration
                    Button(
                        onClick = {
                            val validationError = validateForm(isSignIn, email, password, confirmPassword, fullName)
                            if (validationError.isEmpty()) {
                                // Clear previous messages
                                errorMessage = ""
                                successMessage = ""

                                // Check if network services are available
                                if (authViewModel != null) {
                                    // Use real network authentication
                                    if (isSignIn) {
                                        Timber.d("Attempting login for: $email")
                                        authViewModel.login(email.trim(), password)
                                    } else {
                                        Timber.d("Attempting registration for: $email")
                                        authViewModel.register(email.trim(), password, fullName.trim())
                                    }
                                } else {
                                    // Fallback when network services are not available
                                    Timber.w("AuthViewModel not available, using fallback authentication")
                                    isLoading = true

                                    // Simulate network delay
                                    kotlinx.coroutines.CoroutineScope(kotlinx.coroutines.Dispatchers.Main).launch {
                                        kotlinx.coroutines.delay(2000)

                                        // Simple validation for demo mode
                                        if (email.contains("@") && password.length >= 6) {
                                            successMessage = if (isSignIn) "Demo login successful" else "Demo registration successful"
                                            isLoading = false
                                            kotlinx.coroutines.delay(1000)
                                            onLoginSuccess()
                                        } else {
                                            errorMessage = "Invalid credentials for demo mode"
                                            isLoading = false
                                        }
                                    }
                                }
                            } else {
                                errorMessage = validationError
                                Timber.w("Form validation failed: $validationError")
                            }
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(56.dp)
                            .background(
                                brush = Brush.horizontalGradient(
                                    colors = listOf(
                                        SmartShieldColors.Primary,
                                        SmartShieldColors.Secondary
                                    )
                                ),
                                shape = RoundedCornerShape(8.dp)
                            ),
                        enabled = !isLoading,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color.Transparent,
                            disabledContainerColor = SmartShieldColors.Disabled
                        ),
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        if (isLoading) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(20.dp),
                                color = SmartShieldColors.OnPrimary
                            )
                        } else {
                            Text(
                                text = if (isSignIn)
                                    stringResource(R.string.sign_in).uppercase()
                                else
                                    stringResource(R.string.create_account).uppercase(),
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Bold,
                                color = SmartShieldColors.OnPrimary,
                                fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // Or divider
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        HorizontalDivider(
                            modifier = Modifier.weight(1f),
                            color = SmartShieldColors.Primary.copy(alpha = 0.3f)
                        )
                        Text(
                            text = stringResource(R.string.or_continue_with),
                            modifier = Modifier.padding(horizontal = 16.dp),
                            color = SmartShieldColors.OnSurfaceVariant,
                            fontSize = 12.sp,
                            fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace
                        )
                        HorizontalDivider(
                            modifier = Modifier.weight(1f),
                            color = SmartShieldColors.Primary.copy(alpha = 0.3f)
                        )
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // Enhanced Google Sign-In Button
                    OutlinedButton(
                        onClick = {
                            if (!isLoading) {
                                // Clear previous messages
                                errorMessage = ""
                                successMessage = ""

                                // Check if Google Auth service is available
                                if (googleAuthService != null && authViewModel != null) {
                                    Timber.d("Starting Google Sign-In flow")
                                    isLoading = true

                                    try {
                                        googleAuthService.signIn()
                                    } catch (e: Exception) {
                                        Timber.e(e, "Error starting Google Sign-In")
                                        errorMessage = "Failed to start Google Sign-In"
                                        isLoading = false
                                    }
                                } else {
                                    // Fallback when Google Auth is not available
                                    val missingService = when {
                                        googleAuthService == null -> "Google Sign-In service"
                                        authViewModel == null -> "Authentication service"
                                        else -> "Required services"
                                    }
                                    errorMessage = "$missingService not available"
                                    Timber.w("Google Sign-In not available: $missingService is null")
                                }
                            }
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(56.dp),
                        enabled = !isLoading,
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = SmartShieldColors.OnSurface
                        ),
                        border = BorderStroke(
                            1.dp,
                            SmartShieldColors.Primary.copy(alpha = 0.5f)
                        ),
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.Center
                        ) {
                            Text(
                                text = "G",
                                fontSize = 20.sp,
                                fontWeight = FontWeight.Bold,
                                color = SmartShieldColors.Primary,
                                modifier = Modifier
                                    .background(
                                        SmartShieldColors.Primary.copy(alpha = 0.1f),
                                        RoundedCornerShape(4.dp)
                                    )
                                    .padding(horizontal = 8.dp, vertical = 4.dp)
                            )
                            Spacer(modifier = Modifier.width(12.dp))
                            Text(
                                text = stringResource(R.string.continue_with_google),
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Medium,
                                color = SmartShieldColors.OnSurface,
                                fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // Enhanced Demo Login Button
                    OutlinedButton(
                        onClick = {
                            // Set demo credentials
                            email = "<EMAIL>"
                            password = "demo123"
                            fullName = "Demo User"

                            // Clear previous messages
                            errorMessage = ""
                            successMessage = ""

                            Timber.d("Starting demo login")

                            if (authViewModel != null) {
                                // Try real authentication with demo credentials
                                Timber.d("Using real authentication for demo login")
                                authViewModel.login(email, password)
                            } else {
                                // Fallback demo mode
                                Timber.d("Using fallback demo mode")
                                isLoading = true

                                kotlinx.coroutines.CoroutineScope(kotlinx.coroutines.Dispatchers.Main).launch {
                                    kotlinx.coroutines.delay(1500) // Simulate network delay
                                    successMessage = "Demo login successful"
                                    isLoading = false
                                    kotlinx.coroutines.delay(1000)
                                    onLoginSuccess()
                                }
                            }
                        },
                        modifier = Modifier.fillMaxWidth(),
                        enabled = !isLoading,
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = SmartShieldColors.Primary
                        ),
                        border = BorderStroke(
                            1.dp,
                            SmartShieldColors.Primary.copy(alpha = 0.5f)
                        )
                    ) {
                        Text(
                            text = stringResource(R.string.demo_login).uppercase(),
                            color = SmartShieldColors.Primary,
                            fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(24.dp))

            // Footer
            Text(
                text = stringResource(R.string.secure_encrypted_protected).uppercase(),
                color = SmartShieldColors.OnSurfaceVariant,
                fontSize = 12.sp,
                textAlign = TextAlign.Center,
                fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace,
                letterSpacing = 1.sp
            )
        }
    }
}

@Composable
fun SimpleLoginScreen(
    onLoginSuccess: () -> Unit
) {
    var isSignIn by remember { mutableStateOf(true) }
    var email by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    var confirmPassword by remember { mutableStateOf("") }
    var fullName by remember { mutableStateOf("") }
    var passwordVisible by remember { mutableStateOf(false) }
    var isLoading by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf("") }

    val context = LocalContext.current

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        // Simple Logo and Title
        Text(
            text = "🛡️",
            fontSize = 64.sp,
            modifier = Modifier.padding(16.dp)
        )

        Text(
            text = stringResource(R.string.smart_shield),
            fontSize = 32.sp,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(32.dp))

        // Simple Toggle
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            Button(
                onClick = {
                    isSignIn = true
                    errorMessage = ""
                },
                modifier = Modifier.weight(1f)
            ) {
                Text(text = stringResource(R.string.sign_in))
            }

            Spacer(modifier = Modifier.width(8.dp))

            Button(
                onClick = {
                    isSignIn = false
                    errorMessage = ""
                },
                modifier = Modifier.weight(1f)
            ) {
                Text(text = stringResource(R.string.create_account))
            }
        }

        Spacer(modifier = Modifier.height(24.dp))

        // Simple Form
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = if (isSignIn) stringResource(R.string.welcome_to) else stringResource(R.string.create_account),
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Error Message
                if (errorMessage.isNotEmpty()) {
                    Text(
                        text = errorMessage,
                        color = MaterialTheme.colorScheme.error,
                        modifier = Modifier.padding(8.dp)
                    )
                }

                // Full Name Field (only for sign up)
                if (!isSignIn) {
                    OutlinedTextField(
                        value = fullName,
                        onValueChange = { fullName = it },
                        label = { Text(stringResource(R.string.full_name)) },
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true,
                        enabled = !isLoading
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                }

                // Email Field
                OutlinedTextField(
                    value = email,
                    onValueChange = { email = it },
                    label = { Text(stringResource(R.string.email)) },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Email),
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true,
                    enabled = !isLoading
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Password Field
                OutlinedTextField(
                    value = password,
                    onValueChange = { password = it },
                    label = { Text(stringResource(R.string.password)) },
                    visualTransformation = if (passwordVisible) VisualTransformation.None else PasswordVisualTransformation(),
                    trailingIcon = {
                        IconButton(onClick = { passwordVisible = !passwordVisible }) {
                            Icon(
                                imageVector = if (passwordVisible) Icons.Default.Visibility else Icons.Default.VisibilityOff,
                                contentDescription = null
                            )
                        }
                    },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password),
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true,
                    enabled = !isLoading
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Confirm Password Field (only for sign up)
                if (!isSignIn) {
                    OutlinedTextField(
                        value = confirmPassword,
                        onValueChange = { confirmPassword = it },
                        label = { Text(stringResource(R.string.confirm_password)) },
                        visualTransformation = PasswordVisualTransformation(),
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password),
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true,
                        enabled = !isLoading
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                }

                Spacer(modifier = Modifier.height(24.dp))

                // Main Action Button
                Button(
                    onClick = {
                        val validationError = validateForm(isSignIn, email, password, confirmPassword, fullName)
                        if (validationError.isEmpty()) {
                            errorMessage = ""
                            isLoading = true
                            // Simple demo login for now
                            Timber.d("Login attempt: $email")
                            onLoginSuccess()
                        } else {
                            errorMessage = validationError
                        }
                    },
                    modifier = Modifier.fillMaxWidth(),
                    enabled = !isLoading
                ) {
                    if (isLoading) {
                        CircularProgressIndicator(modifier = Modifier.size(20.dp))
                    } else {
                        Text(
                            text = if (isSignIn)
                                stringResource(R.string.sign_in)
                            else
                                stringResource(R.string.create_account)
                        )
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Demo Login Button
                OutlinedButton(
                    onClick = {
                        email = "<EMAIL>"
                        password = "demo123"
                        fullName = "Demo User"
                        isLoading = true
                        errorMessage = ""
                        Timber.d("Demo login")
                        onLoginSuccess()
                    },
                    modifier = Modifier.fillMaxWidth(),
                    enabled = !isLoading
                ) {
                    Text(text = stringResource(R.string.demo_login))
                }
            }
        }

        Spacer(modifier = Modifier.height(24.dp))

        // Footer
        Text(
            text = stringResource(R.string.secure_encrypted_protected),
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            fontSize = 12.sp,
            textAlign = TextAlign.Center
        )
    }
}

// Enhanced validation function
private fun validateForm(
    isSignIn: Boolean,
    email: String,
    password: String,
    confirmPassword: String,
    fullName: String
): String {
    if (email.isBlank()) {
        return "Email is required"
    }

    // Add email pattern validation back safely
    try {
        if (!android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()) {
            return "Invalid email format"
        }
    } catch (e: Exception) {
        // If pattern matching fails, just check for @ symbol
        if (!email.contains("@")) {
            return "Invalid email format"
        }
    }

    if (password.isBlank()) {
        return "Password is required"
    }

    if (password.length < 6) {
        return "Password must be at least 6 characters"
    }

    if (!isSignIn) {
        if (fullName.isBlank()) {
            return "Full name is required"
        }

        if (fullName.length < 2) {
            return "Name must be at least 2 characters"
        }

        if (confirmPassword.isBlank()) {
            return "Please confirm your password"
        }

        if (password != confirmPassword) {
            return "Passwords do not match"
        }
    }

    return ""
}
