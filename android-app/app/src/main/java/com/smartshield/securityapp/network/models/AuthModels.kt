package com.smartshield.securityapp.network.models

import com.google.gson.annotations.SerializedName

// Request Models
data class RegisterRequest(
    val email: String,
    val password: String,
    @SerializedName("full_name") val fullName: String,
    val phone: String? = null,
    @SerializedName("emergency_contacts") val emergencyContacts: List<EmergencyContact>? = null
)

data class LoginRequest(
    val email: String,
    val password: String,
    @SerializedName("device_info") val deviceInfo: DeviceInfo? = null
)

data class RefreshTokenRequest(
    @SerializedName("refresh_token") val refreshToken: String
)

data class UpdateProfileRequest(
    @SerializedName("full_name") val fullName: String? = null,
    val phone: String? = null,
    @SerializedName("emergency_contacts") val emergencyContacts: List<EmergencyContact>? = null,
    @SerializedName("notification_preferences") val notificationPreferences: NotificationPreferences? = null
)

data class ChangePasswordRequest(
    @SerializedName("current_password") val currentPassword: String,
    @SerializedName("new_password") val newPassword: String
)

data class GoogleAuthRequest(
    @SerializedName("id_token") val idToken: String,
    @SerializedName("device_info") val deviceInfo: DeviceInfo? = null
)

// Password Reset Request Models
data class PasswordResetRequest(
    val email: String,
    @SerializedName("device_info") val deviceInfo: com.smartshield.securityapp.utils.DeviceInfo
)

data class PasswordResetVerifyRequest(
    val email: String,
    @SerializedName("reset_code") val resetCode: String,
    @SerializedName("new_password") val newPassword: String,
    @SerializedName("device_info") val deviceInfo: com.smartshield.securityapp.utils.DeviceInfo
)

// Multi-Factor Authentication Request Models
data class MFASetupRequest(
    val method: String, // TOTP, SMS, EMAIL, BIOMETRIC
    @SerializedName("phone_number") val phoneNumber: String? = null,
    val email: String? = null,
    @SerializedName("device_info") val deviceInfo: Map<String, Any>
)

data class MFAVerifyRequest(
    val method: String, // TOTP, SMS, EMAIL, BIOMETRIC, BACKUP_CODE
    val code: String,
    @SerializedName("session_token") val sessionToken: String,
    @SerializedName("device_info") val deviceInfo: Map<String, Any>
)

// Response Models
data class AuthResponse(
    val success: Boolean,
    val message: String? = null,
    val user: UserProfile? = null,
    @SerializedName("access_token") val accessToken: String? = null,
    @SerializedName("refresh_token") val refreshToken: String? = null,
    @SerializedName("expires_in") val expiresIn: Long? = null,
    @SerializedName("token_type") val tokenType: String? = "Bearer"
)

data class UserProfile(
    val id: String,
    val email: String,
    @SerializedName("full_name") val fullName: String,
    val phone: String? = null,
    val role: String = "user",
    @SerializedName("is_active") val isActive: Boolean = true,
    @SerializedName("email_verified") val emailVerified: Boolean = false,
    @SerializedName("created_at") val createdAt: String,
    @SerializedName("updated_at") val updatedAt: String,
    @SerializedName("emergency_contacts") val emergencyContacts: List<EmergencyContact>? = null,
    @SerializedName("notification_preferences") val notificationPreferences: NotificationPreferences? = null,
    @SerializedName("subscription_plan") val subscriptionPlan: String? = "free",
    @SerializedName("subscription_status") val subscriptionStatus: String? = "active"
)

// Supporting Models
data class EmergencyContact(
    val name: String,
    val phone: String,
    val email: String? = null,
    val relationship: String? = null
)

data class NotificationPreferences(
    @SerializedName("push_enabled") val pushEnabled: Boolean = true,
    @SerializedName("email_enabled") val emailEnabled: Boolean = true,
    @SerializedName("sms_enabled") val smsEnabled: Boolean = false,
    @SerializedName("sos_alerts") val sosAlerts: Boolean = true,
    @SerializedName("location_alerts") val locationAlerts: Boolean = true,
    @SerializedName("device_alerts") val deviceAlerts: Boolean = true,
    @SerializedName("quiet_hours") val quietHours: QuietHours? = null
)

data class QuietHours(
    val enabled: Boolean = false,
    @SerializedName("start_time") val startTime: String = "22:00",
    @SerializedName("end_time") val endTime: String = "07:00"
)

// DeviceInfo is imported from utils package to avoid duplication

// Generic API Response
data class ApiResponse(
    val success: Boolean,
    val message: String? = null,
    val error: String? = null,
    val code: String? = null,
    val data: Any? = null
)

// Error Response
data class ErrorResponse(
    val error: String,
    val message: String? = null,
    val code: String? = null,
    val details: Map<String, Any>? = null
)
