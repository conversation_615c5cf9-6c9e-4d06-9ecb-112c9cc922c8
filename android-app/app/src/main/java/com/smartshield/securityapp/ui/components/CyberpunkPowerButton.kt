package com.smartshield.securityapp.ui.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.PowerSettingsNew
import androidx.compose.material.icons.filled.Stop
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.drawscope.rotate
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.smartshield.securityapp.ui.theme.SmartShieldColors
import kotlin.math.*
import androidx.compose.foundation.layout.RowScope

@Composable
fun CyberpunkPowerButton(
    isActive: Boolean,
    onToggle: () -> Unit,
    modifier: Modifier = Modifier,
    buttonSize: Float = 120f
) {
    val infiniteTransition = rememberInfiniteTransition(label = "power_button")

    // Animation values
    val rotation by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(8000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "rotation"
    )

    val pulseAlpha by infiniteTransition.animateFloat(
        initialValue = 0.4f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "pulse"
    )

    Box(
        modifier = modifier.size(buttonSize.dp),
        contentAlignment = Alignment.Center
    ) {
        // Animated background rings
        Canvas(
            modifier = Modifier.fillMaxSize()
        ) {
            val canvasWidth = size.width
            val canvasHeight = size.height
            val centerX = canvasWidth / 2f
            val centerY = canvasHeight / 2f
            val radius = canvasWidth / 2f * 0.8f

            // Outer glow ring
            drawCircle(
                color = if (isActive) SmartShieldColors.Primary.copy(alpha = pulseAlpha * 0.3f)
                       else SmartShieldColors.Error.copy(alpha = pulseAlpha * 0.3f),
                radius = radius * 1.2f,
                center = Offset(centerX, centerY)
            )

            // Rotating energy rings
            repeat(3) { ring ->
                val ringRadius = radius * (0.7f + ring.toFloat() * 0.1f)
                val ringAlpha = pulseAlpha * (0.8f - ring.toFloat() * 0.2f)
                val ringRotation = rotation + (ring.toFloat() * 120f)

                rotate(ringRotation, pivot = Offset(centerX, centerY)) {
                    drawCircle(
                        color = if (isActive) SmartShieldColors.Secondary.copy(alpha = ringAlpha)
                        else SmartShieldColors.Warning.copy(alpha = ringAlpha),
                        radius = ringRadius,
                        center = Offset(centerX, centerY),
                        style = Stroke(width = 1.5f)
                    )
                }
            }
        }

        // Main power button
        Box(
            modifier = Modifier
                .size((buttonSize * 0.6f).dp)
                .clip(CircleShape)
                .background(
                    brush = Brush.radialGradient(
                        colors = listOf(
                            if (isActive) SmartShieldColors.Primary.copy(alpha = 0.3f)
                            else SmartShieldColors.Error.copy(alpha = 0.3f),
                            SmartShieldColors.CardBackground.copy(alpha = 0.9f),
                            SmartShieldColors.CardBackground
                        ),
                        radius = buttonSize * 0.3f
                    )
                )
                .clickable(
                    interactionSource = remember { MutableInteractionSource() },
                    indication = null
                ) { onToggle() },
            contentAlignment = Alignment.Center
        ) {
            // Power icon with glow effect
            Icon(
                imageVector = if (isActive) Icons.Default.PowerSettingsNew else Icons.Default.Stop,
                contentDescription = if (isActive) "Power On" else "Power Off",
                modifier = Modifier.size((buttonSize * 0.25f).dp),
                tint = if (isActive) SmartShieldColors.Primary else SmartShieldColors.Error
            )
        }

        // Status text below button
        Column(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .offset(y = (buttonSize * 0.4f).dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = if (isActive) "STOP" else "START",
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold,
                color = if (isActive) SmartShieldColors.Error else SmartShieldColors.Primary,
                fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace
            )

            Text(
                text = if (isActive) "MONITORING ACTIVE" else "SYSTEM OFFLINE",
                fontSize = 10.sp,
                color = SmartShieldColors.OnSurfaceVariant,
                fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace
            )
        }
    }
}

@Composable
fun CyberpunkStatusDisplay(
    title: String,
    value: String,
    isOnline: Boolean,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = SmartShieldColors.CardBackground.copy(alpha = 0.9f)
        ),
        border = androidx.compose.foundation.BorderStroke(
            1.dp,
            if (isOnline) SmartShieldColors.Primary.copy(alpha = 0.3f)
            else SmartShieldColors.Error.copy(alpha = 0.3f)
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = title,
                fontSize = 12.sp,
                color = SmartShieldColors.OnSurfaceVariant,
                fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = value,
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = if (isOnline) SmartShieldColors.Primary else SmartShieldColors.Error,
                fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace
            )
        }
    }
}
