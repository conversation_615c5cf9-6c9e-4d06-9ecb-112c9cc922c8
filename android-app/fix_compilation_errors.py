#!/usr/bin/env python3
"""
Smart Shield Android App - Compilation Error Fix
Fixes all compilation errors shown in Android Studio
"""

import os
import sys
import subprocess
import re
from pathlib import Path

def clean_gradle_cache():
    """Clean Gradle cache and build directories"""
    print("🧹 Cleaning Gradle cache and build directories...")
    
    try:
        # Clean build directories
        if os.path.exists("build"):
            subprocess.run(["rm", "-rf", "build"], check=True)
        if os.path.exists("app/build"):
            subprocess.run(["rm", "-rf", "app/build"], check=True)
        if os.path.exists(".gradle"):
            subprocess.run(["rm", "-rf", ".gradle"], check=True)
            
        print("✅ Build directories cleaned")
        
        # Clean Gradle cache
        gradle_cache = os.path.expanduser("~/.gradle/caches")
        if os.path.exists(gradle_cache):
            subprocess.run(["rm", "-rf", gradle_cache], check=True)
            print("✅ Gradle cache cleaned")
            
    except Exception as e:
        print(f"⚠️ Warning: Could not clean all cache directories: {e}")

def fix_import_issues():
    """Fix import issues in key files"""
    print("🔧 Fixing import issues...")
    
    # Files that need import fixes
    files_to_fix = [
        "app/src/main/java/com/smartshield/securityapp/services/MultiFactorAuthService.kt",
        "app/src/main/java/com/smartshield/securityapp/services/PasswordResetService.kt",
        "app/src/main/java/com/smartshield/securityapp/ui/LoginActivity.kt",
        "app/src/main/java/com/smartshield/securityapp/ui/DashboardActivity.kt"
    ]
    
    for file_path in files_to_fix:
        if not os.path.exists(file_path):
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Ensure proper imports are present
            imports_to_add = []
            
            if "NetworkResult" in content and "import com.smartshield.securityapp.network.NetworkResult" not in content:
                imports_to_add.append("import com.smartshield.securityapp.network.NetworkResult")
            
            if "safeApiCall" in content and "import com.smartshield.securityapp.network.safeApiCall" not in content:
                imports_to_add.append("import com.smartshield.securityapp.network.safeApiCall")
            
            if "DeviceInfo" in content and "import com.smartshield.securityapp.utils.DeviceInfo" not in content:
                imports_to_add.append("import com.smartshield.securityapp.utils.DeviceInfo")
            
            if imports_to_add:
                # Find the last import line
                lines = content.split('\n')
                last_import_index = -1
                
                for i, line in enumerate(lines):
                    if line.strip().startswith("import "):
                        last_import_index = i
                
                if last_import_index >= 0:
                    # Insert new imports after the last import
                    for import_line in imports_to_add:
                        lines.insert(last_import_index + 1, import_line)
                        last_import_index += 1
                    
                    # Write back to file
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write('\n'.join(lines))
                    
                    print(f"✅ Fixed imports in {file_path}")
                    
        except Exception as e:
            print(f"❌ Error fixing imports in {file_path}: {e}")

def fix_duplicate_definitions():
    """Fix duplicate class definitions"""
    print("🔧 Fixing duplicate definitions...")
    
    # Check AuthModels.kt for duplicate DeviceInfo
    auth_models_path = "app/src/main/java/com/smartshield/securityapp/network/models/AuthModels.kt"
    if os.path.exists(auth_models_path):
        try:
            with open(auth_models_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Remove duplicate DeviceInfo definition if it exists
            if content.count("data class DeviceInfo") > 0:
                # Replace the DeviceInfo definition with a comment
                pattern = r'data class DeviceInfo\([^)]*\)'
                replacement = '// DeviceInfo is imported from utils package'
                content = re.sub(pattern, replacement, content, flags=re.MULTILINE | re.DOTALL)
                
                with open(auth_models_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print("✅ Fixed duplicate DeviceInfo definition")
                
        except Exception as e:
            print(f"❌ Error fixing duplicate definitions: {e}")

def fix_component_imports():
    """Fix UI component import issues"""
    print("🔧 Fixing UI component imports...")
    
    # Ensure all components are properly exported
    component_files = [
        "app/src/main/java/com/smartshield/securityapp/ui/components/AdvancedCyberpunkEffects.kt",
        "app/src/main/java/com/smartshield/securityapp/ui/components/CyberpunkBackground.kt",
        "app/src/main/java/com/smartshield/securityapp/ui/components/CyberpunkPowerButton.kt",
        "app/src/main/java/com/smartshield/securityapp/ui/components/SmartShieldComponents.kt"
    ]
    
    for file_path in component_files:
        if not os.path.exists(file_path):
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Ensure proper imports for Compose
            required_imports = [
                "import androidx.compose.foundation.layout.RowScope",
                "import androidx.compose.runtime.*",
                "import androidx.compose.ui.Modifier"
            ]
            
            lines = content.split('\n')
            imports_added = False
            
            for required_import in required_imports:
                if required_import not in content:
                    # Find the last import line
                    last_import_index = -1
                    for i, line in enumerate(lines):
                        if line.strip().startswith("import "):
                            last_import_index = i
                    
                    if last_import_index >= 0:
                        lines.insert(last_import_index + 1, required_import)
                        imports_added = True
            
            if imports_added:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(lines))
                print(f"✅ Fixed component imports in {file_path}")
                
        except Exception as e:
            print(f"❌ Error fixing component imports in {file_path}: {e}")

def regenerate_gradle_wrapper():
    """Regenerate Gradle wrapper if needed"""
    print("🔧 Checking Gradle wrapper...")
    
    try:
        # Check if gradlew is executable
        if not os.access("gradlew", os.X_OK):
            os.chmod("gradlew", 0o755)
            print("✅ Made gradlew executable")
        
        # Try to run gradle wrapper
        result = subprocess.run(["./gradlew", "--version"], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Gradle wrapper is working")
        else:
            print("⚠️ Gradle wrapper has issues, but continuing...")
            
    except Exception as e:
        print(f"⚠️ Warning: Gradle wrapper check failed: {e}")

def invalidate_android_studio_cache():
    """Create script to invalidate Android Studio cache"""
    print("📝 Creating Android Studio cache invalidation instructions...")
    
    instructions = """
# Android Studio Cache Invalidation Instructions

To fix compilation errors in Android Studio:

1. **Invalidate Caches and Restart**:
   - Go to File → Invalidate Caches and Restart
   - Select "Invalidate and Restart"

2. **Clean and Rebuild**:
   - Go to Build → Clean Project
   - Then Build → Rebuild Project

3. **Sync Gradle**:
   - Click the "Sync Now" button if it appears
   - Or go to File → Sync Project with Gradle Files

4. **Restart Android Studio**:
   - Close Android Studio completely
   - Reopen the project

5. **Check SDK and Build Tools**:
   - Go to File → Project Structure → SDK Location
   - Ensure Android SDK is properly configured
   - Check that build tools version matches build.gradle

These steps should resolve most compilation errors related to caching and indexing.
"""
    
    with open("ANDROID_STUDIO_FIX.md", 'w') as f:
        f.write(instructions)
    
    print("✅ Created ANDROID_STUDIO_FIX.md with instructions")

def main():
    """Main fix function"""
    print("🔧 Smart Shield Compilation Error Fix")
    print("=" * 50)
    
    # Change to android-app directory if not already there
    if not os.path.exists("app/build.gradle"):
        if os.path.exists("android-app/app/build.gradle"):
            os.chdir("android-app")
        else:
            print("❌ Error: Could not find Android app directory")
            sys.exit(1)
    
    # Run all fixes
    clean_gradle_cache()
    fix_import_issues()
    fix_duplicate_definitions()
    fix_component_imports()
    regenerate_gradle_wrapper()
    invalidate_android_studio_cache()
    
    print("\n" + "=" * 50)
    print("✅ All compilation fixes applied!")
    print("\n📋 Next Steps:")
    print("   1. Open Android Studio")
    print("   2. Follow instructions in ANDROID_STUDIO_FIX.md")
    print("   3. Invalidate caches and restart")
    print("   4. Clean and rebuild project")
    print("\n🎉 Your Smart Shield app should now compile successfully!")

if __name__ == "__main__":
    main()
