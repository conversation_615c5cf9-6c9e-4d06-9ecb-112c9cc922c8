#!/usr/bin/env python3
"""
Smart Shield Android App - Compilation Fix Test
Tests all the compilation fixes applied to resolve Android Studio errors
"""

import os
import sys
import subprocess
import re
from pathlib import Path

def check_kotlin_syntax():
    """Check Kotlin syntax in all files"""
    print("🔍 Checking Kotlin syntax...")
    
    kotlin_files = []
    for root, dirs, files in os.walk("app/src/main/java"):
        for file in files:
            if file.endswith(".kt"):
                kotlin_files.append(os.path.join(root, file))
    
    print(f"Found {len(kotlin_files)} Kotlin files")
    
    # Check for common syntax issues
    issues = []
    
    for file_path in kotlin_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # Check for unresolved references
                if "MatchGroupCollection" in content:
                    issues.append(f"{file_path}: Contains MatchGroupCollection reference")
                
                # Check for missing imports (skip NetworkModule.kt as it defines these)
                filename = os.path.basename(file_path)
                if filename != "NetworkModule.kt":
                    if "NetworkResult" in content and "import com.smartshield.securityapp.network.NetworkResult" not in content:
                        issues.append(f"{file_path}: Missing NetworkResult import")

                    if "safeApiCall" in content and "import com.smartshield.securityapp.network.safeApiCall" not in content:
                        issues.append(f"{file_path}: Missing safeApiCall import")
                
                # Check for duplicate DeviceInfo definitions
                if content.count("data class DeviceInfo") > 1:
                    issues.append(f"{file_path}: Contains duplicate DeviceInfo definition")
                
        except Exception as e:
            issues.append(f"{file_path}: Error reading file - {e}")
    
    return issues

def check_component_definitions():
    """Check that all UI components are properly defined"""
    print("🔍 Checking UI component definitions...")
    
    required_components = [
        "CyberpunkBackground",
        "CyberpunkButton", 
        "CyberpunkStatusDisplay",
        "CyberpunkPowerButton",
        "GlitchEffect",
        "HolographicDataStream",
        "SmartShieldButton",
        "SmartShieldCard",
        "StatusBadge"
    ]
    
    component_files = [
        "app/src/main/java/com/smartshield/securityapp/ui/components/AdvancedCyberpunkEffects.kt",
        "app/src/main/java/com/smartshield/securityapp/ui/components/CyberpunkBackground.kt",
        "app/src/main/java/com/smartshield/securityapp/ui/components/CyberpunkPowerButton.kt",
        "app/src/main/java/com/smartshield/securityapp/ui/components/SmartShieldComponents.kt"
    ]
    
    found_components = set()
    issues = []
    
    for file_path in component_files:
        if not os.path.exists(file_path):
            issues.append(f"Missing component file: {file_path}")
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
                for component in required_components:
                    if f"fun {component}" in content:
                        found_components.add(component)
                        
        except Exception as e:
            issues.append(f"Error reading {file_path}: {e}")
    
    # Check for missing components
    missing_components = set(required_components) - found_components
    for component in missing_components:
        issues.append(f"Missing component definition: {component}")
    
    print(f"✅ Found {len(found_components)} components: {', '.join(sorted(found_components))}")
    
    return issues

def check_network_models():
    """Check network models and API definitions"""
    print("🔍 Checking network models...")
    
    issues = []
    
    # Check NetworkResult definition
    network_module_path = "app/src/main/java/com/smartshield/securityapp/network/NetworkModule.kt"
    if os.path.exists(network_module_path):
        with open(network_module_path, 'r', encoding='utf-8') as f:
            content = f.read()
            if "sealed class NetworkResult" not in content:
                issues.append("NetworkResult class not found in NetworkModule.kt")
            if "suspend fun <T> safeApiCall" not in content:
                issues.append("safeApiCall function not found in NetworkModule.kt")
    else:
        issues.append("NetworkModule.kt file not found")
    
    # Check API service
    api_service_path = "app/src/main/java/com/smartshield/securityapp/network/ApiService.kt"
    if os.path.exists(api_service_path):
        with open(api_service_path, 'r', encoding='utf-8') as f:
            content = f.read()
            required_methods = ["setupMFA", "verifyMFA", "requestPasswordReset", "verifyPasswordReset"]
            for method in required_methods:
                if f"suspend fun {method}" not in content:
                    issues.append(f"Missing API method: {method}")
    else:
        issues.append("ApiService.kt file not found")
    
    return issues

def check_imports():
    """Check for proper imports in key files"""
    print("🔍 Checking imports...")
    
    issues = []
    
    key_files = [
        "app/src/main/java/com/smartshield/securityapp/services/MultiFactorAuthService.kt",
        "app/src/main/java/com/smartshield/securityapp/services/PasswordResetService.kt",
        "app/src/main/java/com/smartshield/securityapp/ui/LoginActivity.kt",
        "app/src/main/java/com/smartshield/securityapp/ui/DashboardActivity.kt"
    ]
    
    for file_path in key_files:
        if not os.path.exists(file_path):
            issues.append(f"Missing key file: {file_path}")
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # Check for proper package declaration
                if not content.startswith("package com.smartshield.securityapp"):
                    issues.append(f"{file_path}: Missing or incorrect package declaration")
                
                # Check for required imports based on usage (skip NetworkModule.kt as it defines these)
                filename = os.path.basename(file_path)
                if filename != "NetworkModule.kt":
                    if "NetworkResult" in content and "import com.smartshield.securityapp.network.NetworkResult" not in content:
                        issues.append(f"{file_path}: Missing NetworkResult import")

                    if "safeApiCall" in content and "import com.smartshield.securityapp.network.safeApiCall" not in content:
                        issues.append(f"{file_path}: Missing safeApiCall import")
                
        except Exception as e:
            issues.append(f"Error checking imports in {file_path}: {e}")
    
    return issues

def main():
    """Main test function"""
    print("🧪 Smart Shield Compilation Fix Test")
    print("=" * 50)
    
    # Change to android-app directory if not already there
    if not os.path.exists("app/build.gradle"):
        if os.path.exists("android-app/app/build.gradle"):
            os.chdir("android-app")
        else:
            print("❌ Error: Could not find Android app directory")
            sys.exit(1)
    
    all_issues = []
    
    # Run all checks
    all_issues.extend(check_kotlin_syntax())
    all_issues.extend(check_component_definitions())
    all_issues.extend(check_network_models())
    all_issues.extend(check_imports())
    
    print("\n" + "=" * 50)
    
    if all_issues:
        print(f"❌ Found {len(all_issues)} issues:")
        for issue in all_issues:
            print(f"   • {issue}")
        print("\n🔧 Please fix these issues before compilation")
        return False
    else:
        print("✅ All compilation checks passed!")
        print("🎉 Smart Shield Android app should compile successfully!")
        print("\n📋 Summary:")
        print("   • Kotlin syntax: ✅ Clean")
        print("   • UI components: ✅ All defined")
        print("   • Network models: ✅ Properly configured")
        print("   • Imports: ✅ All correct")
        print("\n🚀 Ready for Android Studio build!")
        return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
